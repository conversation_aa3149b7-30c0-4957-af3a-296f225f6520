#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
tkinter内置图片功能演示
展示不依赖PIL库的图片显示功能
"""

import tkinter as tk
from tkinter import ttk, messagebox
import os

def demo_tkinter_image():
    """演示tkinter内置图片功能"""
    
    # 创建演示窗口
    root = tk.Tk()
    root.title("tkinter内置图片功能演示")
    root.geometry("700x500")
    root.resizable(False, False)
    
    # 标题
    title_label = tk.Label(root, text="tkinter内置图片功能演示", 
                          font=("微软雅黑", 16, "bold"), fg="blue")
    title_label.pack(pady=15)
    
    # 功能说明
    desc_text = """
修改后的图片显示功能特点：

✓ 不依赖PIL库，使用tkinter内置的PhotoImage
✓ 支持PNG和GIF格式图片
✓ 自动检测图片尺寸并进行适当缩放
✓ 增加了图片显示尺寸（350x180）
✓ 去除了边框效果，直接贴在右下角
✓ 提供简洁的备用显示方案
    """
    
    desc_label = tk.Label(root, text=desc_text, font=("微软雅黑", 11), 
                         justify=tk.LEFT, anchor="nw", fg="darkgreen")
    desc_label.pack(fill=tk.X, padx=20, pady=10)
    
    # 分隔线
    separator = tk.Frame(root, height=2, bg="gray")
    separator.pack(fill=tk.X, padx=20, pady=10)
    
    # 图片测试区域
    test_frame = tk.Frame(root)
    test_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
    
    # 左侧：技术说明
    left_frame = tk.Frame(test_frame)
    left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))
    
    tech_title = tk.Label(left_frame, text="技术实现", 
                         font=("微软雅黑", 14, "bold"))
    tech_title.pack(anchor="w", pady=(0, 10))
    
    tech_details = """
主要改进：

1. 移除PIL依赖
   • 使用tk.PhotoImage()直接加载PNG
   • 支持PNG和GIF格式
   • 无需额外安装库

2. 智能尺寸处理
   • 检测原图尺寸：591x107
   • 目标显示尺寸：350x180
   • 使用subsample()方法缩放

3. 简洁显示
   • 去除边框效果
   • 背景透明融合
   • 右下角对齐

4. 错误处理
   • 文件存在性检查
   • 格式兼容性检查
   • 优雅的备用方案
    """
    
    tech_label = tk.Label(left_frame, text=tech_details, 
                         font=("微软雅黑", 10), justify=tk.LEFT, anchor="nw")
    tech_label.pack(fill=tk.BOTH, expand=True)
    
    # 右侧：图片预览
    right_frame = tk.Frame(test_frame)
    right_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=(10, 0))
    
    preview_title = tk.Label(right_frame, text="图片预览", 
                            font=("微软雅黑", 14, "bold"))
    preview_title.pack(pady=(0, 10))
    
    # 检查并显示图片
    image_path = "软件信息.png"
    
    if os.path.exists(image_path):
        try:
            # 使用tkinter加载图片
            image = tk.PhotoImage(file=image_path)
            
            # 获取尺寸
            width = image.width()
            height = image.height()
            
            # 显示图片信息
            info_text = f"图片文件: {image_path}\n原始尺寸: {width} x {height}"
            info_label = tk.Label(right_frame, text=info_text, 
                                 font=("微软雅黑", 9), fg="gray")
            info_label.pack(pady=5)
            
            # 计算显示尺寸
            display_width = 350
            display_height = 180
            
            if width <= display_width and height <= display_height:
                # 原图较小，直接显示
                display_image = image
                actual_width, actual_height = width, height
                scale_info = "使用原始尺寸"
            else:
                # 原图较大，进行缩放
                scale_x = max(1, width // display_width)
                scale_y = max(1, height // display_height)
                scale_factor = max(scale_x, scale_y)
                
                display_image = image.subsample(scale_factor, scale_factor)
                actual_width = width // scale_factor
                actual_height = height // scale_factor
                scale_info = f"缩放因子: {scale_factor}"
            
            # 显示缩放信息
            scale_label = tk.Label(right_frame, 
                                  text=f"显示尺寸: {actual_width} x {actual_height}\n{scale_info}", 
                                  font=("微软雅黑", 9), fg="blue")
            scale_label.pack(pady=5)
            
            # 显示图片（模拟在界面右下角的效果）
            image_label = tk.Label(right_frame, image=display_image, 
                                  bg=right_frame.cget('bg'))
            image_label.pack(pady=10)
            image_label.image = display_image  # 保持引用
            
            # 成功提示
            success_label = tk.Label(right_frame, text="✓ 图片加载成功", 
                                    font=("微软雅黑", 10), fg="green")
            success_label.pack(pady=5)
            
        except Exception as e:
            error_label = tk.Label(right_frame, text=f"加载失败:\n{str(e)}", 
                                  font=("微软雅黑", 10), fg="red")
            error_label.pack(pady=20)
    else:
        # 文件不存在
        error_label = tk.Label(right_frame, text="图片文件不存在", 
                              font=("微软雅黑", 12), fg="red")
        error_label.pack(pady=20)
    
    # 底部按钮
    button_frame = tk.Frame(root)
    button_frame.pack(fill=tk.X, pady=15)
    
    def launch_main_program():
        """启动主程序"""
        try:
            import subprocess
            subprocess.Popen(["python", "metro_evacuation_gui_v2.py"])
            messagebox.showinfo("启动成功", 
                              "主程序已启动！\n\n"
                              "请查看右下角的软件信息图片。\n"
                              "现在使用tkinter内置功能，无需PIL库。")
        except Exception as e:
            messagebox.showerror("启动失败", f"无法启动主程序：{str(e)}")
    
    def show_code_changes():
        """显示代码修改"""
        code_window = tk.Toplevel(root)
        code_window.title("主要代码修改")
        code_window.geometry("650x450")
        
        code_text = '''
主要修改内容：

1. 移除PIL依赖：
   # 原来的代码
   from PIL import Image, ImageTk
   
   # 修改后
   # 移除PIL依赖，使用tkinter内置图片支持

2. 使用tkinter内置PhotoImage：
   # 直接加载PNG图片
   self.software_info_image = tk.PhotoImage(file=image_path)
   
   # 获取图片尺寸
   original_width = self.software_info_image.width()
   original_height = self.software_info_image.height()

3. 简单缩放处理：
   # 设置目标尺寸（增加了图片大小）
   display_width = 350   # 增加宽度
   display_height = 180  # 增加高度
   
   # 计算缩放因子
   scale_x = max(1, original_width // display_width)
   scale_y = max(1, original_height // display_height)
   scale_factor = max(scale_x, scale_y)
   
   # 使用subsample缩放
   final_image = self.software_info_image.subsample(scale_factor, scale_factor)

4. 简洁显示（去除边框）：
   # 创建图片标签，直接显示
   image_label = tk.Label(info_frame, image=final_image, 
                         bg=parent.cget('bg'))  # 背景融合
   image_label.pack(anchor=tk.E, padx=(0, 10), pady=5)

5. 优化备用方案：
   # 简洁的备用显示
   info_label = tk.Label(parent, text="软件信息图片\\n(加载失败)",
                        bg=parent.cget('bg'))  # 背景融合
        '''
        
        text_widget = tk.Text(code_window, wrap=tk.WORD, font=("Consolas", 9))
        text_widget.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        text_widget.insert(tk.END, code_text)
        text_widget.config(state=tk.DISABLED)
    
    # 按钮
    tk.Button(button_frame, text="启动主程序", command=launch_main_program,
             font=("微软雅黑", 12, "bold"), bg="green", fg="white",
             width=12, height=2).pack(side=tk.LEFT, padx=20)
    
    tk.Button(button_frame, text="查看代码修改", command=show_code_changes,
             font=("微软雅黑", 12), bg="blue", fg="white",
             width=12, height=2).pack(side=tk.LEFT, padx=10)
    
    tk.Button(button_frame, text="关闭演示", command=root.destroy,
             font=("微软雅黑", 12), bg="red", fg="white",
             width=12, height=2).pack(side=tk.RIGHT, padx=20)
    
    # 版本信息
    version_label = tk.Label(root, text="tkinter内置图片功能 - 无需PIL库依赖", 
                            font=("微软雅黑", 9), fg="gray")
    version_label.pack(pady=5)
    
    root.mainloop()

if __name__ == "__main__":
    demo_tkinter_image()
