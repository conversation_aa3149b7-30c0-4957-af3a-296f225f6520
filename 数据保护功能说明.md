# 地铁站台疏散时间计算器 - 数据保护功能说明

## 功能概述

本次更新为地铁站台疏散时间计算器添加了完整的数据保护和备份功能，确保用户数据的安全性和可靠性。

## 新增功能

### 1. 数据修改检测与保护

#### 1.1 实时修改检测
- **功能描述**：程序会实时监控用户对数据的修改
- **实现方式**：通过 `is_data_modified` 标志和数据快照对比
- **用户体验**：窗口标题会显示 "*" 标记表示有未保存的修改

#### 1.2 退出保护
- **触发时机**：用户关闭程序窗口时
- **保护机制**：如果有未保存的修改，会弹出确认对话框
- **选项说明**：
  - **是**：保存后退出
  - **否**：不保存直接退出
  - **取消**：返回程序继续编辑

#### 1.3 加载数据保护
- **触发时机**：用户点击"读取数据"按钮时
- **保护机制**：检查当前是否有未保存的修改
- **选项说明**：
  - **是**：保存当前修改后加载新数据
  - **否**：不保存直接加载新数据
  - **取消**：取消加载操作

#### 1.4 重置数据保护
- **触发时机**：用户点击"重置"按钮时
- **保护机制**：检查当前是否有未保存的修改
- **选项说明**：
  - **是**：保存当前修改后重置
  - **否**：不保存直接重置
  - **取消**：取消重置操作

### 2. 改进的另存为功能

#### 2.1 智能命名建议
- **默认命名**：当前项目名_副本_时间戳
- **示例**：`测试站点_副本_20250801_1430`
- **便利性**：自动选中文本，方便用户修改

#### 2.2 名称冲突检测
- **实时检查**：输入站点名称时实时检查是否已存在
- **状态提示**：
  - 绿色 "✓ 名称可用"：名称未被使用
  - 橙色 "⚠ 该名称已存在，保存将覆盖现有项目"：名称冲突警告

#### 2.3 覆盖确认机制
- **触发条件**：保存数据时检测到项目已存在且数据有修改
- **选项说明**：
  - **是**：另存为新项目（推荐）
  - **否**：覆盖当前项目
  - **取消**：取消保存操作

### 3. 自动备份系统

#### 3.1 自动备份机制
- **备份频率**：每5分钟自动备份一次
- **备份位置**：程序目录下的 `backups` 文件夹
- **备份命名**：`data_backup_YYYYMMDD_HHMMSS.db`
- **后台运行**：使用独立线程，不影响程序性能

#### 3.2 备份文件管理
- **数量限制**：最多保留10个备份文件
- **自动清理**：超出数量时自动删除最旧的备份
- **时间排序**：按文件修改时间排序，保留最新的备份

### 4. 手动备份功能

#### 4.1 备份管理菜单
- **访问方式**：点击"备份"按钮打开菜单
- **菜单选项**：
  - 手动备份
  - 从备份恢复
  - 查看备份文件夹
  - 清理旧备份

#### 4.2 手动备份
- **操作方式**：用户可随时创建备份
- **保存位置**：用户自定义选择
- **文件格式**：标准SQLite数据库文件(.db)

#### 4.3 备份恢复
- **安全机制**：恢复前自动备份当前数据库
- **确认对话**：明确告知用户恢复操作的影响
- **重启提示**：恢复后提示用户重启程序

#### 4.4 备份文件夹管理
- **快速访问**：直接打开备份文件夹
- **手动清理**：可手动清理超出数量限制的旧备份
- **清理确认**：删除前会显示详细信息并要求确认

## 技术实现

### 1. 数据修改检测
```python
def on_data_changed(self, event=None):
    """数据变化时的回调"""
    self.is_data_modified = True
    self.update_window_title()

def get_current_data_snapshot(self):
    """获取当前数据的快照，用于检测变化"""
    # 实现数据快照功能
```

### 2. 自动备份线程
```python
def start_auto_backup(self):
    """启动自动备份线程"""
    self.backup_running = True
    self.backup_thread = threading.Thread(target=self._auto_backup_worker, daemon=True)
    self.backup_thread.start()
```

### 3. 窗口关闭事件处理
```python
def on_closing(self):
    """窗口关闭事件处理"""
    if self.is_data_modified:
        # 显示保存确认对话框
        # 处理用户选择
```

## 使用指南

### 1. 日常使用
1. **正常编辑**：程序会自动检测数据修改，窗口标题显示修改状态
2. **保存数据**：修改后及时保存，避免数据丢失
3. **另存为**：需要创建副本时使用另存为功能

### 2. 备份管理
1. **查看备份**：通过"备份"菜单查看自动创建的备份文件
2. **手动备份**：重要操作前可手动创建备份
3. **定期清理**：定期清理旧备份文件，节省磁盘空间

### 3. 数据恢复
1. **意外丢失**：可从自动备份中恢复数据
2. **版本回退**：可恢复到之前的数据版本
3. **安全恢复**：恢复前会自动备份当前数据

## 配置选项

### 可调整参数
- `auto_backup_enabled`：是否启用自动备份（默认：True）
- `backup_interval`：自动备份间隔秒数（默认：300秒/5分钟）
- `max_backup_files`：最大备份文件数量（默认：10个）

### 修改方法
在 `__init__` 方法中修改相应参数：
```python
self.auto_backup_enabled = True
self.backup_interval = 300  # 5分钟
self.max_backup_files = 10  # 最多10个备份
```

## 注意事项

1. **备份文件夹**：程序会在当前目录创建 `backups` 文件夹
2. **磁盘空间**：定期清理备份文件以节省磁盘空间
3. **数据安全**：重要数据建议额外手动备份到其他位置
4. **程序重启**：从备份恢复数据后需要重启程序
5. **权限问题**：确保程序对当前目录有读写权限

## 更新总结

本次更新显著提升了程序的数据安全性和用户体验：

✅ **完善的数据保护机制**：防止意外数据丢失
✅ **智能的另存为功能**：提供更好的用户体验
✅ **可靠的自动备份系统**：确保数据安全
✅ **便捷的备份管理工具**：方便用户管理备份文件
✅ **友好的用户界面**：清晰的状态提示和确认对话框

这些功能确保了用户数据的安全性，提供了多层次的数据保护，让用户可以放心使用程序进行重要的工程计算工作。
