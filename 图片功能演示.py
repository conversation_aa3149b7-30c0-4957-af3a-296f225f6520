#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
图片功能演示脚本
展示在GUI右下角添加软件信息图片的功能
"""

import tkinter as tk
from tkinter import ttk, messagebox
import os

try:
    from PIL import Image, ImageTk
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False

def demo_image_feature():
    """演示图片功能"""
    
    # 创建演示窗口
    root = tk.Tk()
    root.title("软件信息图片功能演示")
    root.geometry("800x600")
    root.resizable(False, False)
    
    # 标题
    title_label = tk.Label(root, text="软件信息图片功能演示", 
                          font=("微软雅黑", 16, "bold"), fg="blue")
    title_label.pack(pady=20)
    
    # 功能说明
    desc_text = """
本次更新在地铁疏散计算器的GUI界面右下角添加了软件信息图片显示功能。

主要特点：
• 自动加载"软件信息.png"图片文件
• 智能调整图片大小以适应界面布局
• 保持图片原始宽高比
• 右对齐显示，位置美观
• 支持PIL库的高质量图片缩放
• 提供备用显示方案（当图片加载失败时）
    """
    
    desc_label = tk.Label(root, text=desc_text, font=("微软雅黑", 11), 
                         justify=tk.LEFT, anchor="nw")
    desc_label.pack(fill=tk.X, padx=20, pady=10)
    
    # 分隔线
    separator = tk.Frame(root, height=2, bg="gray")
    separator.pack(fill=tk.X, padx=20, pady=10)
    
    # 技术实现区域
    tech_frame = tk.Frame(root)
    tech_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
    
    # 左侧：技术实现说明
    left_frame = tk.Frame(tech_frame)
    left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))
    
    tech_title = tk.Label(left_frame, text="技术实现", 
                         font=("微软雅黑", 14, "bold"))
    tech_title.pack(anchor="w", pady=(0, 10))
    
    tech_details = """
1. 图片加载与处理：
   • 使用PIL库加载PNG图片
   • 计算合适的显示尺寸
   • 保持宽高比进行缩放
   
2. 界面集成：
   • 在右侧内容区域底部添加
   • 使用ttk.Frame容器
   • 右对齐显示
   
3. 错误处理：
   • 检查图片文件是否存在
   • PIL库可用性检测
   • 提供备用显示方案
   
4. 代码位置：
   • create_right_content()方法
   • create_software_info_section()方法
   • create_fallback_info_display()方法
    """
    
    tech_label = tk.Label(left_frame, text=tech_details, 
                         font=("微软雅黑", 10), justify=tk.LEFT, anchor="nw")
    tech_label.pack(fill=tk.BOTH, expand=True)
    
    # 右侧：图片预览
    right_frame = tk.Frame(tech_frame)
    right_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=(10, 0))
    
    preview_title = tk.Label(right_frame, text="图片预览", 
                            font=("微软雅黑", 14, "bold"))
    preview_title.pack(pady=(0, 10))
    
    # 检查图片文件并显示
    image_path = "软件信息.png"
    
    if os.path.exists(image_path) and PIL_AVAILABLE:
        try:
            # 加载并显示图片
            image = Image.open(image_path)
            
            # 调整大小用于预览
            max_width = 200
            max_height = 150
            
            original_width, original_height = image.size
            width_ratio = max_width / original_width
            height_ratio = max_height / original_height
            scale_ratio = min(width_ratio, height_ratio)
            
            new_width = int(original_width * scale_ratio)
            new_height = int(original_height * scale_ratio)
            
            image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)
            photo = ImageTk.PhotoImage(image)
            
            # 显示图片
            image_label = tk.Label(right_frame, image=photo, relief="ridge", bd=2)
            image_label.pack(pady=10)
            image_label.image = photo  # 保持引用
            
            # 显示图片信息
            info_text = f"原始尺寸: {original_width} x {original_height}\n显示尺寸: {new_width} x {new_height}"
            info_label = tk.Label(right_frame, text=info_text, 
                                 font=("微软雅黑", 9), fg="gray")
            info_label.pack(pady=5)
            
        except Exception as e:
            error_label = tk.Label(right_frame, text=f"图片加载失败:\n{str(e)}", 
                                  font=("微软雅黑", 10), fg="red")
            error_label.pack(pady=20)
    else:
        # 显示状态信息
        if not os.path.exists(image_path):
            status_text = "图片文件不存在"
            color = "red"
        elif not PIL_AVAILABLE:
            status_text = "PIL库不可用"
            color = "orange"
        else:
            status_text = "未知错误"
            color = "red"
        
        status_label = tk.Label(right_frame, text=status_text, 
                               font=("微软雅黑", 12), fg=color)
        status_label.pack(pady=20)
        
        # 显示备用方案
        fallback_label = tk.Label(right_frame, text="软件信息图片\n(图片加载失败)", 
                                 font=("微软雅黑", 9), fg="darkgray",
                                 bg="white", relief="ridge", bd=1,
                                 width=20, height=4, justify=tk.CENTER)
        fallback_label.pack(pady=10)
    
    # 底部按钮
    button_frame = tk.Frame(root)
    button_frame.pack(fill=tk.X, pady=20)
    
    def launch_main_program():
        """启动主程序"""
        try:
            import subprocess
            subprocess.Popen(["python", "metro_evacuation_gui_v2.py"])
            messagebox.showinfo("启动成功", "主程序已启动！\n请查看右下角的软件信息图片。")
        except Exception as e:
            messagebox.showerror("启动失败", f"无法启动主程序：{str(e)}")
    
    def show_implementation():
        """显示实现代码"""
        code_window = tk.Toplevel(root)
        code_window.title("实现代码")
        code_window.geometry("600x400")
        
        code_text = '''
# 在 create_right_content 方法中添加：
def create_right_content(self, parent):
    """创建右侧内容区域"""
    # 中间计算值区域
    self.create_intermediate_section(parent)
    
    # 最终结果区域
    self.create_results_section(parent)
    
    # 软件信息图片区域 (新增)
    self.create_software_info_section(parent)

# 新增的图片显示方法：
def create_software_info_section(self, parent):
    """创建软件信息图片区域"""
    try:
        image_path = "软件信息.png"
        if not os.path.exists(image_path):
            return
        
        info_frame = ttk.Frame(parent)
        info_frame.pack(fill=tk.X, pady=(10, 0))
        
        if PIL_AVAILABLE:
            # 使用PIL加载和调整图片
            image = Image.open(image_path)
            # 计算合适尺寸并缩放
            # 创建tkinter图片对象
            # 显示在右下角
        else:
            # 备用显示方案
            self.create_fallback_info_display(info_frame)
    except Exception as e:
        print(f"创建软件信息区域失败: {e}")
        '''
        
        text_widget = tk.Text(code_window, wrap=tk.WORD, font=("Consolas", 10))
        text_widget.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        text_widget.insert(tk.END, code_text)
        text_widget.config(state=tk.DISABLED)
    
    # 按钮
    tk.Button(button_frame, text="启动主程序", command=launch_main_program,
             font=("微软雅黑", 12, "bold"), bg="green", fg="white",
             width=12, height=2).pack(side=tk.LEFT, padx=20)
    
    tk.Button(button_frame, text="查看实现代码", command=show_implementation,
             font=("微软雅黑", 12), bg="blue", fg="white",
             width=12, height=2).pack(side=tk.LEFT, padx=10)
    
    tk.Button(button_frame, text="关闭演示", command=root.destroy,
             font=("微软雅黑", 12), bg="red", fg="white",
             width=12, height=2).pack(side=tk.RIGHT, padx=20)
    
    # 版本信息
    version_label = tk.Label(root, text="软件信息图片功能 - 2025年8月1日更新", 
                            font=("微软雅黑", 9), fg="gray")
    version_label.pack(pady=5)
    
    root.mainloop()

if __name__ == "__main__":
    demo_image_feature()
