#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据保护功能演示脚本
演示地铁疏散计算器的新增数据保护功能
"""

import tkinter as tk
from tkinter import messagebox
import os
import time

def demo_data_protection():
    """演示数据保护功能"""
    
    def show_feature(title, description, features):
        """显示功能介绍"""
        feature_text = "\n".join([f"• {feature}" for feature in features])
        messagebox.showinfo(
            title,
            f"{description}\n\n主要功能：\n{feature_text}"
        )
    
    # 创建演示窗口
    root = tk.Tk()
    root.title("数据保护功能演示")
    root.geometry("600x500")
    root.resizable(False, False)
    
    # 标题
    title_label = tk.Label(root, text="地铁疏散计算器 - 数据保护功能演示", 
                          font=("微软雅黑", 16, "bold"), fg="blue")
    title_label.pack(pady=20)
    
    # 功能介绍区域
    intro_frame = tk.Frame(root)
    intro_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
    
    # 功能列表
    features = [
        {
            "title": "1. 数据修改检测",
            "desc": "实时监控数据变化，防止意外丢失",
            "details": [
                "窗口标题显示修改状态（*标记）",
                "退出程序时检查未保存修改",
                "加载数据前保护当前修改",
                "重置数据前确认保存"
            ]
        },
        {
            "title": "2. 智能另存为功能",
            "desc": "提供更好的另存为体验",
            "details": [
                "智能默认命名（项目名_副本_时间戳）",
                "实时检测名称冲突",
                "自动选中文本便于修改",
                "覆盖确认机制"
            ]
        },
        {
            "title": "3. 自动备份系统",
            "desc": "定期自动备份，确保数据安全",
            "details": [
                "每5分钟自动备份一次",
                "最多保留10个备份文件",
                "后台运行不影响性能",
                "自动清理旧备份文件"
            ]
        },
        {
            "title": "4. 手动备份管理",
            "desc": "完整的备份管理工具",
            "details": [
                "随时手动创建备份",
                "从备份文件恢复数据",
                "查看和管理备份文件夹",
                "安全的恢复机制"
            ]
        }
    ]
    
    # 创建功能按钮
    for i, feature in enumerate(features):
        btn_frame = tk.Frame(intro_frame)
        btn_frame.pack(fill=tk.X, pady=5)
        
        # 功能按钮
        feature_btn = tk.Button(
            btn_frame,
            text=feature["title"],
            font=("微软雅黑", 12, "bold"),
            bg="lightblue",
            fg="darkblue",
            width=25,
            height=2,
            command=lambda f=feature: show_feature(f["title"], f["desc"], f["details"])
        )
        feature_btn.pack(side=tk.LEFT, padx=5)
        
        # 描述文本
        desc_label = tk.Label(
            btn_frame,
            text=feature["desc"],
            font=("微软雅黑", 10),
            fg="gray",
            anchor="w"
        )
        desc_label.pack(side=tk.LEFT, padx=10, fill=tk.X, expand=True)
    
    # 分隔线
    separator = tk.Frame(intro_frame, height=2, bg="gray")
    separator.pack(fill=tk.X, pady=20)
    
    # 演示说明
    demo_label = tk.Label(
        intro_frame,
        text="演示说明：",
        font=("微软雅黑", 12, "bold"),
        anchor="w"
    )
    demo_label.pack(fill=tk.X, pady=(0, 5))
    
    demo_text = """
1. 点击上方按钮查看各功能的详细说明
2. 运行主程序体验完整的数据保护功能
3. 尝试修改数据、保存、加载等操作
4. 观察窗口标题的修改状态提示
5. 使用备份菜单管理数据备份
    """
    
    demo_desc = tk.Label(
        intro_frame,
        text=demo_text,
        font=("微软雅黑", 10),
        justify=tk.LEFT,
        anchor="nw"
    )
    demo_desc.pack(fill=tk.BOTH, expand=True)
    
    # 底部按钮
    bottom_frame = tk.Frame(root)
    bottom_frame.pack(fill=tk.X, pady=10)
    
    def launch_main_program():
        """启动主程序"""
        try:
            import subprocess
            subprocess.Popen(["python", "metro_evacuation_gui_v2.py"])
            messagebox.showinfo("启动成功", "主程序已启动！\n请在主程序中体验数据保护功能。")
        except Exception as e:
            messagebox.showerror("启动失败", f"无法启动主程序：{str(e)}")
    
    def show_backup_info():
        """显示备份信息"""
        backup_dir = "backups"
        if os.path.exists(backup_dir):
            backup_files = [f for f in os.listdir(backup_dir) if f.endswith('.db')]
            count = len(backup_files)
            if count > 0:
                latest = max(backup_files, key=lambda f: os.path.getmtime(os.path.join(backup_dir, f)))
                latest_time = time.ctime(os.path.getmtime(os.path.join(backup_dir, latest)))
                info = f"备份文件夹：{backup_dir}\n备份数量：{count} 个\n最新备份：{latest}\n备份时间：{latest_time}"
            else:
                info = f"备份文件夹：{backup_dir}\n备份数量：0 个\n提示：运行主程序后会自动创建备份"
        else:
            info = "备份文件夹尚未创建\n提示：运行主程序后会自动创建备份文件夹"
        
        messagebox.showinfo("备份信息", info)
    
    # 按钮
    tk.Button(
        bottom_frame,
        text="启动主程序",
        font=("微软雅黑", 12, "bold"),
        bg="green",
        fg="white",
        width=15,
        height=2,
        command=launch_main_program
    ).pack(side=tk.LEFT, padx=10)
    
    tk.Button(
        bottom_frame,
        text="查看备份信息",
        font=("微软雅黑", 12),
        bg="orange",
        fg="white",
        width=15,
        height=2,
        command=show_backup_info
    ).pack(side=tk.LEFT, padx=10)
    
    tk.Button(
        bottom_frame,
        text="关闭演示",
        font=("微软雅黑", 12),
        bg="red",
        fg="white",
        width=15,
        height=2,
        command=root.destroy
    ).pack(side=tk.RIGHT, padx=10)
    
    # 版本信息
    version_label = tk.Label(
        root,
        text="数据保护功能 v1.0 - 2025年8月1日更新",
        font=("微软雅黑", 9),
        fg="gray"
    )
    version_label.pack(pady=5)
    
    # 运行演示
    root.mainloop()

if __name__ == "__main__":
    demo_data_protection()
