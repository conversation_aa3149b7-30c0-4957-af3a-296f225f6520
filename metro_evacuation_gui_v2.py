#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
地铁站台疏散时间计算器 GUI V1.0
支持数据库存储、Excel导出等功能
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import tkinter.font as tkFont
from metro_evacuation_calculator_v2 import MetroEvacuationCalculatorV2
from database_manager import DatabaseManager
from excel_exporter import ExcelExporter
import os

class MetroEvacuationGUIV2:
    """地铁站台疏散时间计算器GUI V1.0"""
    
    def __init__(self, root):
        self.root = root
        self.root.title("地铁站台疏散时间计算器 V1.0")
        self.root.geometry("1200x650")  # 进一步减小宽度和高度，界面更紧凑
        self.root.resizable(False, False)  # 禁止调整窗口大小
        
        # 创建计算器和管理器实例
        self.calculator = MetroEvacuationCalculatorV2()
        self.db_manager = DatabaseManager("data.db")  # 指定固定的数据库文件
        self.excel_exporter = ExcelExporter()
        
        # 当前项目信息
        self.current_station_name = None
        self.is_data_modified = False
        
        # 设置字体
        self.font_title = tkFont.Font(family="微软雅黑", size=14, weight="bold")
        self.font_label = tkFont.Font(family="微软雅黑", size=10)
        self.font_entry = tkFont.Font(family="微软雅黑", size=9)
        
        self.create_widgets()
        self.load_default_values()
    
    def create_widgets(self):
        """创建界面组件"""
        # 主标题
        title_label = tk.Label(self.root, text="地铁站台疏散时间计算器 V1.0", 
                              font=self.font_title, fg="blue")
        title_label.pack(pady=5)
        
        # 创建主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # 创建顶部按钮区域
        self.create_button_section(main_frame)
        
        # 创建滚动区域
        self.create_scrollable_area(main_frame)
    
    def create_button_section(self, parent):
        """创建按钮区域"""
        button_frame = ttk.Frame(parent)
        button_frame.pack(fill=tk.X, pady=(0, 10))

        # 所有按钮紧贴左侧排列
        buttons_container = ttk.Frame(button_frame)
        buttons_container.pack(side=tk.LEFT, anchor='w')

        # 获取程序背景色
        bg_color = self.root.cget('bg')

        # 按钮样式配置 - 减小按钮尺寸
        button_config = {
            'font': ('微软雅黑', 10, 'bold'),  # 适中的字体大小
            'width': 8,   # 减小宽度
            'height': 1,  # 减小高度
            'relief': 'raised',
            'bd': 1,      # 减小边框
            'bg': bg_color,  # 使用程序背景色
            'fg': 'black'    # 黑色文字
        }

        # 计算按钮
        calc_button = tk.Button(buttons_container, text="计算",
                               command=self.calculate, **button_config)
        calc_button.pack(side=tk.LEFT, padx=3)

        # 重置按钮
        reset_button = tk.Button(buttons_container, text="重置",
                                command=self.reset_values, **button_config)
        reset_button.pack(side=tk.LEFT, padx=3)

        # 保存数据按钮
        save_button = tk.Button(buttons_container, text="保存数据",
                               command=self.save_data, **button_config)
        save_button.pack(side=tk.LEFT, padx=3)

        # 读取数据按钮
        load_button = tk.Button(buttons_container, text="读取数据",
                               command=self.load_data, **button_config)
        load_button.pack(side=tk.LEFT, padx=3)

        # 导出Excel按钮
        export_button = tk.Button(buttons_container, text="导出Excel",
                                 command=self.export_excel, **button_config)
        export_button.pack(side=tk.LEFT, padx=3)

        # 批量导出按钮
        batch_export_button = tk.Button(buttons_container, text="批量导出",
                                       command=self.batch_export_excel, **button_config)
        batch_export_button.pack(side=tk.LEFT, padx=3)

        # 帮助按钮
        help_button = tk.Button(buttons_container, text="帮助",
                               command=self.show_help, **button_config)
        help_button.pack(side=tk.LEFT, padx=3)
    
    def create_scrollable_area(self, parent):
        """创建左右分栏的内容区域"""
        # 创建主容器
        main_container = ttk.Frame(parent)
        main_container.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 左侧区域
        left_frame = ttk.Frame(main_container)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))

        # 右侧区域
        right_frame = ttk.Frame(main_container)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(5, 0))

        # 在左右区域中创建内容
        self.create_left_content(left_frame)
        self.create_right_content(right_frame)
    
    def create_left_content(self, parent):
        """创建左侧内容区域"""
        # 客流数据区域
        self.create_passenger_flow_section(parent)

        # 计算参数区域
        self.create_parameters_section(parent)

    def create_right_content(self, parent):
        """创建右侧内容区域"""
        # 中间计算值区域
        self.create_intermediate_section(parent)

        # 最终结果区域
        self.create_results_section(parent)
    
    def create_passenger_flow_section(self, parent):
        """创建客流数据区域"""
        # 客流数据组
        passenger_group = ttk.LabelFrame(parent, text="客流数据（人）", padding=10)
        passenger_group.pack(fill=tk.X, pady=(0, 10))
        
        # 创建表格样式的布局
        # 表头
        headers = ["", "上行", "", "", "下行", "", ""]
        sub_headers = ["", "上客", "下客", "断面", "上客", "下客", "断面"]
        
        # 第一行表头
        for col, header in enumerate(headers):
            if header:
                if header == "上行":
                    label = tk.Label(passenger_group, text=header, font=self.font_label, bg="lightgray")
                    label.grid(row=0, column=col, columnspan=3, sticky="ew", padx=1, pady=1)
                elif header == "下行":
                    label = tk.Label(passenger_group, text=header, font=self.font_label, bg="lightgray")
                    label.grid(row=0, column=col, columnspan=3, sticky="ew", padx=1, pady=1)
        
        # 第二行表头
        for col, header in enumerate(sub_headers):
            if header:
                label = tk.Label(passenger_group, text=header, font=self.font_label, bg="lightblue")
                label.grid(row=1, column=col, sticky="ew", padx=1, pady=1)
        
        # 数据行
        self.passenger_entries = {}
        periods = [("初期", "initial"), ("近期", "near"), ("远期", "far")]
        
        for row, (period_name, period_key) in enumerate(periods, 2):
            # 阶段名称
            label = tk.Label(passenger_group, text=period_name, font=self.font_label)
            label.grid(row=row, column=0, sticky="w", padx=5, pady=2)
            
            # 输入框
            entries = {}
            flow_types = ['up_in', 'up_out', 'up_area', 'down_in', 'down_out', 'down_area']
            for col, flow_type in enumerate(flow_types, 1):
                entry = tk.Entry(passenger_group, font=self.font_entry, width=10, justify='center')
                entry.grid(row=row, column=col, padx=1, pady=2)
                entry.bind('<KeyRelease>', self.on_data_changed)
                entries[flow_type] = entry
            
            self.passenger_entries[period_key] = entries
        
        # 设置列权重
        for col in range(7):
            passenger_group.columnconfigure(col, weight=1)
    
    def create_parameters_section(self, parent):
        """创建计算参数区域"""
        param_group = ttk.LabelFrame(parent, text="计算参数", padding=10)
        param_group.pack(fill=tk.X, pady=(0, 10))
        
        # 表头
        headers = ["", "初期", "近期", "远期", "单位"]
        for col, header in enumerate(headers):
            label = tk.Label(param_group, text=header, font=self.font_label, bg="lightgray")
            label.grid(row=0, column=col, sticky="ew", padx=1, pady=1)
        
        # 参数列表 - 现在所有参数都支持三个阶段
        self.param_entries = {}
        params = [
            ("高峰系数", "k", "/"),
            ("列车对数", "n_train", "/"),
            ("站台人流密度ρ", "p", "人/m²"),
            ("站台长度L", "l", "m"),
            ("站台边缘到列车全部停稳内侧距离M", "m", "m"),
            ("一台自动扶梯的通过能力A1", "a_1", "人/h·m"),
            ("疏散楼梯的通过能力A2", "a_2", "人/h·m"),
            ("自动扶梯台数N", "n_auto", "/"),
            ("疏散楼梯宽度B", "b", "m"),
            ("疏散楼梯数量", "n_stair", "/")
        ]
        
        for row, (param_name, param_key, unit) in enumerate(params, 1):
            # 参数名称
            label = tk.Label(param_group, text=param_name, font=self.font_label)
            label.grid(row=row, column=0, sticky="w", padx=5, pady=2)

            # 所有参数都支持三个阶段
            entries = {}
            for col, period in enumerate(['initial', 'near', 'far'], 1):
                entry = tk.Entry(param_group, font=self.font_entry, width=10, justify='center')
                entry.grid(row=row, column=col, padx=1, pady=2)
                entry.bind('<KeyRelease>', self.on_data_changed)
                entries[period] = entry
            self.param_entries[param_key] = entries

            # 单位
            label = tk.Label(param_group, text=unit, font=self.font_label)
            label.grid(row=row, column=4, sticky="w", padx=5, pady=2)
        
        # 设置列权重
        for col in range(5):
            param_group.columnconfigure(col, weight=1)
    
    def on_data_changed(self, event=None):
        """数据变化时的回调"""
        self.is_data_modified = True

    def create_intermediate_section(self, parent):
        """创建中间计算值区域"""
        intermediate_group = ttk.LabelFrame(parent, text="中间计算值", padding=10)
        intermediate_group.pack(fill=tk.X, pady=(0, 10))

        # 表头
        headers = ["", "初期", "近期", "远期", "单位"]
        for col, header in enumerate(headers):
            label = tk.Label(intermediate_group, text=header, font=self.font_label, bg="lightgray")
            label.grid(row=0, column=col, sticky="ew", padx=1, pady=1)

        # 中间计算值
        self.intermediate_labels = {}
        intermediate_values = [
            ("单侧上下客合计（上行）", "up_add", "人"),
            ("单侧上下客合计（下行）", "down_add", "人"),
            ("上客流量合计", "in_add", "人")
        ]

        for row, (value_name, value_key, unit) in enumerate(intermediate_values, 1):
            # 名称
            label = tk.Label(intermediate_group, text=value_name, font=self.font_label)
            label.grid(row=row, column=0, sticky="w", padx=5, pady=2)

            # 结果显示
            labels = {}
            for col, period in enumerate(['initial', 'near', 'far'], 1):
                result_label = tk.Label(intermediate_group, text="0.00", font=self.font_entry,
                                      bg="white", relief="sunken", width=12)
                result_label.grid(row=row, column=col, padx=1, pady=2)
                labels[period] = result_label

            self.intermediate_labels[value_key] = labels

            # 单位
            label = tk.Label(intermediate_group, text=unit, font=self.font_label)
            label.grid(row=row, column=4, sticky="w", padx=5, pady=2)

        # 设置列权重
        for col in range(5):
            intermediate_group.columnconfigure(col, weight=1)

    def create_results_section(self, parent):
        """创建最终结果区域"""
        results_group = ttk.LabelFrame(parent, text="最终计算结果", padding=10)
        results_group.pack(fill=tk.X, pady=(0, 10))

        # 表头
        headers = ["", "初期", "近期", "远期", "单位"]
        for col, header in enumerate(headers):
            label = tk.Label(results_group, text=header, font=self.font_label, bg="lightgray")
            label.grid(row=0, column=col, sticky="ew", padx=1, pady=1)

        # 最终结果
        self.result_labels = {}
        final_results = [
            ("站台宽度b1", "b_1", "m"),
            ("站台宽度b2", "b_2", "m"),
            ("Q1", "q_1", "人"),
            ("Q2", "q_2", "人"),
            ("疏散时间T", "t", "min")
        ]

        for row, (result_name, result_key, unit) in enumerate(final_results, 1):
            # 名称
            label = tk.Label(results_group, text=result_name, font=self.font_label)
            label.grid(row=row, column=0, sticky="w", padx=5, pady=2)

            # 结果显示
            labels = {}
            for col, period in enumerate(['initial', 'near', 'far'], 1):
                result_label = tk.Label(results_group, text="0.000000", font=self.font_entry,
                                      bg="lightyellow", relief="sunken", width=12)
                result_label.grid(row=row, column=col, padx=1, pady=2)
                labels[period] = result_label

            self.result_labels[result_key] = labels

            # 单位
            label = tk.Label(results_group, text=unit, font=self.font_label)
            label.grid(row=row, column=4, sticky="w", padx=5, pady=2)

        # 设置列权重
        for col in range(5):
            results_group.columnconfigure(col, weight=1)

    def load_default_values(self):
        """加载默认值"""
        # 设置默认的计算参数 - 所有参数都支持三个阶段
        defaults = {
            'k': {'initial': '1.13', 'near': '1.13', 'far': '1.13'},
            'n_train': {'initial': '20', 'near': '20', 'far': '20'},
            'p': {'initial': '0.5', 'near': '0.5', 'far': '0.5'},
            'l': {'initial': '135.6', 'near': '135.6', 'far': '135.6'},
            'm': {'initial': '0.3', 'near': '0.3', 'far': '0.3'},
            'a_1': {'initial': '7300', 'near': '7300', 'far': '7300'},
            'a_2': {'initial': '3700', 'near': '3700', 'far': '3700'},
            'n_auto': {'initial': '2', 'near': '2', 'far': '2'},
            'b': {'initial': '3.3', 'near': '3.3', 'far': '3.3'},
            'n_stair': {'initial': '1', 'near': '1', 'far': '1'}
        }

        for key, periods in defaults.items():
            if key in self.param_entries:
                for period, val in periods.items():
                    self.param_entries[key][period].insert(0, val)



    def clear_all_entries(self):
        """清空所有输入框"""
        # 清空客流数据
        for period_entries in self.passenger_entries.values():
            for entry in period_entries.values():
                entry.delete(0, tk.END)

        # 清空参数 - 现在所有参数都是字典形式
        for param_entry in self.param_entries.values():
            for entry in param_entry.values():
                entry.delete(0, tk.END)

    def reset_values(self):
        """重置所有值"""
        self.clear_all_entries()
        self.load_default_values()

        # 清空结果显示
        for period_labels in self.intermediate_labels.values():
            for label in period_labels.values():
                label.config(text="0")  # 中间计算值显示整数

        for period_labels in self.result_labels.values():
            for label in period_labels.values():
                label.config(text="0.000")  # 最终结果显示3位小数

        self.current_station_name = None
        self.is_data_modified = False

    def get_input_values(self):
        """获取输入值"""
        try:
            # 获取客流数据
            passenger_data = {}
            for period, entries in self.passenger_entries.items():
                period_data = {}
                for key, entry in entries.items():
                    value = entry.get().strip()
                    if not value:
                        value = "0"
                    period_data[key] = float(value)
                passenger_data[period] = period_data

            # 获取计算参数 - 现在所有参数都支持三个阶段
            param_data = {}
            for key, period_entries in self.param_entries.items():
                for period, entry in period_entries.items():
                    value = entry.get().strip()
                    if not value:
                        # 设置默认值
                        if key == "k":
                            value = "1.13"
                        elif key in ["n_train", "n_auto", "n_stair"]:
                            value = "20" if key == "n_train" else ("2" if key == "n_auto" else "1")
                        elif key in ["p", "l", "m", "a_1", "a_2", "b"]:
                            defaults = {"p": "0.5", "l": "135.6", "m": "0.3",
                                      "a_1": "7300", "a_2": "3700", "b": "3.3"}
                            value = defaults.get(key, "0")
                        else:
                            value = "0"
                    param_data[f'{key}_{period}'] = float(value)

            return passenger_data, param_data

        except ValueError as e:
            messagebox.showerror("输入错误", f"请检查输入数据格式: {str(e)}")
            return None, None

    def calculate(self):
        """执行计算"""
        passenger_data, param_data = self.get_input_values()
        if passenger_data is None or param_data is None:
            return

        try:
            # 设置客流数据
            for period, data in passenger_data.items():
                self.calculator.set_passenger_flow(
                    period, data['up_in'], data['up_out'], data['up_area'],
                    data['down_in'], data['down_out'], data['down_area']
                )

            # 设置计算参数
            self.calculator.set_parameters(**param_data)

            # 执行计算
            self.calculator.calculate_all_periods()

            # 更新结果显示
            self.update_results()

            messagebox.showinfo("计算完成", "所有阶段计算完成！")

        except Exception as e:
            messagebox.showerror("计算错误", f"计算过程中出现错误：{str(e)}")

    def update_results(self):
        """更新结果显示"""
        periods = ['initial', 'near', 'far']

        # 更新中间计算值 - 保留整数
        for value_key, period_labels in self.intermediate_labels.items():
            for period in periods:
                result = self.calculator.get_period_results(period)
                period_labels[period].config(text=f"{result[value_key]:.0f}")

        # 更新最终结果 - 保留3位小数
        for result_key, period_labels in self.result_labels.items():
            for period in periods:
                result = self.calculator.get_period_results(period)
                period_labels[period].config(text=f"{result[result_key]:.3f}")

    def save_data(self):
        """保存数据"""
        # 检查是否有数据需要保存
        passenger_data, param_data = self.get_input_values()
        if passenger_data is None or param_data is None:
            return

        # 如果当前有项目且数据已修改，询问是否覆盖
        if self.current_station_name and self.is_data_modified:
            result = messagebox.askyesnocancel(
                "保存确认",
                f"当前项目 '{self.current_station_name}' 的数据已修改。\n\n"
                "是：另存为新项目\n"
                "否：覆盖当前项目\n"
                "取消：不保存"
            )

            if result is None:  # 取消
                return
            elif result:  # 是，另存为
                station_name = self.get_station_name()
                if not station_name:
                    return
            else:  # 否，覆盖
                station_name = self.current_station_name
        else:
            # 新项目或未修改，直接输入站名
            station_name = self.get_station_name()
            if not station_name:
                return

        # 获取所有数据
        all_data = self.calculator.get_all_data()
        all_data['station_name'] = station_name

        # 保存到数据库
        if self.db_manager.save_project(station_name, all_data):
            messagebox.showinfo("保存成功", f"项目 '{station_name}' 保存成功！")
            self.current_station_name = station_name
            self.is_data_modified = False
        else:
            messagebox.showerror("保存失败", "保存数据时出现错误！")

    def get_station_name(self):
        """获取站点名称"""
        dialog = tk.Toplevel(self.root)
        dialog.title("输入站点名称")
        dialog.geometry("300x150")
        dialog.transient(self.root)
        dialog.grab_set()

        # 居中显示
        dialog.geometry("+%d+%d" % (self.root.winfo_rootx() + 50, self.root.winfo_rooty() + 50))

        station_name = tk.StringVar()

        tk.Label(dialog, text="请输入站点名称:", font=self.font_label).pack(pady=10)

        entry = tk.Entry(dialog, textvariable=station_name, font=self.font_entry, width=30)
        entry.pack(pady=10)
        entry.focus()

        button_frame = tk.Frame(dialog)
        button_frame.pack(pady=10)

        result = [None]

        def on_ok():
            name = station_name.get().strip()
            if name:
                result[0] = name
                dialog.destroy()
            else:
                messagebox.showerror("错误", "请输入有效的站点名称！")

        def on_cancel():
            dialog.destroy()

        tk.Button(button_frame, text="确定", command=on_ok).pack(side=tk.LEFT, padx=5)
        tk.Button(button_frame, text="取消", command=on_cancel).pack(side=tk.LEFT, padx=5)

        # 绑定回车键
        entry.bind('<Return>', lambda event: on_ok())

        dialog.wait_window()
        return result[0]

    def load_data(self):
        """读取数据"""
        # 创建项目选择对话框
        self.show_project_selector()

    def show_project_selector(self):
        """显示项目选择器"""
        dialog = tk.Toplevel(self.root)
        dialog.title("选择项目")
        dialog.geometry("600x400")
        dialog.transient(self.root)
        dialog.grab_set()

        # 居中显示
        dialog.geometry("+%d+%d" % (self.root.winfo_rootx() + 100, self.root.winfo_rooty() + 100))

        # 搜索框
        search_frame = tk.Frame(dialog)
        search_frame.pack(fill=tk.X, padx=10, pady=5)

        tk.Label(search_frame, text="搜索:", font=self.font_label).pack(side=tk.LEFT)
        search_var = tk.StringVar()
        search_entry = tk.Entry(search_frame, textvariable=search_var, font=self.font_entry)
        search_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)

        def on_search():
            keyword = search_var.get().strip()
            if keyword:
                projects = self.db_manager.search_projects(keyword)
            else:
                projects = self.db_manager.get_all_projects()
            update_project_list(projects)

        tk.Button(search_frame, text="搜索", command=on_search).pack(side=tk.RIGHT)

        # 项目列表
        list_frame = tk.Frame(dialog)
        list_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # 创建Treeview
        columns = ('station_name', 'update_time')
        tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=15)
        tree.heading('station_name', text='站点名称')
        tree.heading('update_time', text='更新时间')
        tree.column('station_name', width=200)
        tree.column('update_time', width=200)

        # 滚动条
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=tree.yview)
        tree.configure(yscrollcommand=scrollbar.set)

        tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        def update_project_list(projects):
            """更新项目列表"""
            tree.delete(*tree.get_children())
            for station_name, update_time in projects:
                tree.insert('', tk.END, values=(station_name, update_time))

        # 初始加载所有项目
        projects = self.db_manager.get_all_projects()
        update_project_list(projects)

        # 按钮框
        button_frame = tk.Frame(dialog)
        button_frame.pack(fill=tk.X, padx=10, pady=5)

        def on_load():
            selection = tree.selection()
            if selection:
                item = tree.item(selection[0])
                station_name = item['values'][0]
                self.load_project_data(station_name)
                dialog.destroy()
            else:
                messagebox.showwarning("提示", "请选择一个项目！")

        def on_delete():
            selection = tree.selection()
            if selection:
                item = tree.item(selection[0])
                station_name = item['values'][0]
                if messagebox.askyesno("确认删除", f"确定要删除项目 '{station_name}' 吗？"):
                    if self.db_manager.delete_project(station_name):
                        messagebox.showinfo("删除成功", f"项目 '{station_name}' 已删除！")
                        # 刷新列表
                        projects = self.db_manager.get_all_projects()
                        update_project_list(projects)
                    else:
                        messagebox.showerror("删除失败", "删除项目时出现错误！")
            else:
                messagebox.showwarning("提示", "请选择一个项目！")

        tk.Button(button_frame, text="加载", command=on_load, bg="lightgreen").pack(side=tk.LEFT, padx=5)
        tk.Button(button_frame, text="删除", command=on_delete, bg="lightcoral").pack(side=tk.LEFT, padx=5)
        tk.Button(button_frame, text="取消", command=dialog.destroy).pack(side=tk.RIGHT, padx=5)

        # 双击加载
        tree.bind('<Double-1>', lambda e: on_load())

        # 绑定搜索
        search_entry.bind('<KeyRelease>', lambda e: on_search())

    def load_project_data(self, station_name):
        """加载项目数据"""
        project_data = self.db_manager.load_project(station_name)
        if project_data:
            # 清空当前数据
            self.clear_all_entries()

            # 加载客流数据
            periods = ['initial', 'near', 'far']
            for period in periods:
                for flow_type in ['up_in', 'up_out', 'up_area', 'down_in', 'down_out', 'down_area']:
                    key = f'{period}_{flow_type}'
                    if key in project_data:
                        self.passenger_entries[period][flow_type].insert(0, str(project_data[key]))

            # 加载计算参数
            for param_key, entry_or_dict in self.param_entries.items():
                if isinstance(entry_or_dict, dict):
                    # 每个阶段不同的参数
                    for period in periods:
                        key = f'{param_key}_{period}'
                        if key in project_data:
                            entry_or_dict[period].insert(0, str(project_data[key]))
                else:
                    # 所有阶段相同的参数
                    if param_key in project_data:
                        entry_or_dict.insert(0, str(project_data[param_key]))

            # 加载计算结果
            self.calculator.load_data(project_data)
            self.update_results()

            self.current_station_name = station_name
            self.is_data_modified = False

            messagebox.showinfo("加载成功", f"项目 '{station_name}' 加载成功！")
        else:
            messagebox.showerror("加载失败", f"无法加载项目 '{station_name}'！")

    def export_excel(self):
        """导出当前项目到Excel"""
        if not self.current_station_name:
            messagebox.showwarning("提示", "请先保存或加载一个项目！")
            return

        # 选择保存路径
        file_path = filedialog.asksaveasfilename(
            title="导出Excel文件",
            defaultextension=".xlsx",
            filetypes=[("Excel文件", "*.xlsx"), ("所有文件", "*.*")],
            initialfile=f"{self.current_station_name}.xlsx"
        )

        if file_path:
            project_data = self.db_manager.load_project(self.current_station_name)
            if project_data:
                if self.excel_exporter.export_single_project(project_data, file_path):
                    messagebox.showinfo("导出成功", f"项目已导出到: {file_path}")
                else:
                    messagebox.showerror("导出失败", "导出Excel文件时出现错误！")
            else:
                messagebox.showerror("导出失败", "无法获取项目数据！")

    def batch_export_excel(self):
        """批量导出项目到Excel"""
        projects = self.db_manager.get_all_projects()
        if not projects:
            messagebox.showwarning("提示", "没有可导出的项目！")
            return

        # 选择保存路径
        file_path = filedialog.asksaveasfilename(
            title="批量导出Excel文件",
            defaultextension=".xlsx",
            filetypes=[("Excel文件", "*.xlsx"), ("所有文件", "*.*")],
            initialfile="批量导出.xlsx"
        )

        if file_path:
            # 获取所有项目数据
            projects_data = []
            for station_name, _ in projects:
                project_data = self.db_manager.load_project(station_name)
                if project_data:
                    projects_data.append(project_data)

            if projects_data:
                if self.excel_exporter.export_multiple_projects(projects_data, file_path):
                    messagebox.showinfo("导出成功", f"已导出 {len(projects_data)} 个项目到: {file_path}")
                else:
                    messagebox.showerror("导出失败", "批量导出Excel文件时出现错误！")
            else:
                messagebox.showerror("导出失败", "无法获取项目数据！")

    def show_help(self):
        """显示帮助文档"""
        # 尝试打开外部帮助文档
        help_files = ["帮助文档.rtf", "帮助文档.docx", "帮助文档.doc"]
        help_file_found = None

        for help_file in help_files:
            if os.path.exists(help_file):
                help_file_found = help_file
                break

        if help_file_found:
            try:
                # 使用系统默认程序打开文档
                if os.name == 'nt':  # Windows
                    os.startfile(help_file_found)
                elif os.name == 'posix':  # macOS and Linux
                    os.system(f'open "{help_file_found}"')
                return
            except Exception as e:
                print(f"无法打开帮助文档: {e}")

        # 如果无法打开外部文档，则显示内置帮助
        self.show_builtin_help()

    def show_builtin_help(self):
        """显示内置帮助文档"""
        help_window = tk.Toplevel(self.root)
        help_window.title("地铁站台疏散时间计算器 V2.0 - 帮助文档")
        help_window.geometry("900x700")
        help_window.transient(self.root)
        help_window.grab_set()

        # 居中显示
        help_window.geometry("+%d+%d" % (self.root.winfo_rootx() + 150, self.root.winfo_rooty() + 50))

        # 创建滚动文本框
        main_frame = ttk.Frame(help_window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 创建滚动条
        scrollbar = ttk.Scrollbar(main_frame)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 创建文本框
        text_widget = tk.Text(main_frame, wrap=tk.WORD, yscrollcommand=scrollbar.set,
                             font=('微软雅黑', 10), bg='white', fg='black')
        text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.config(command=text_widget.yview)

        # 帮助文档内容
        help_content = self.get_help_content()
        text_widget.insert(tk.END, help_content)
        text_widget.config(state=tk.DISABLED)  # 设为只读

        # 关闭按钮
        close_button = tk.Button(help_window, text="关闭", command=help_window.destroy,
                               font=('微软雅黑', 10), width=10)
        close_button.pack(pady=10)

    def get_help_content(self):
        """获取帮助文档内容"""
        return """地铁站台疏散时间计算器 V1.0 - 帮助文档

═══════════════════════════════════════════════════════════════

📋 概述

地铁站台疏散时间计算器 V1.0 是一个专业的地铁站台疏散分析工具，支持三个阶段（初期、近期、远期）的计算，具备数据库存储、Excel导出等完整功能。

✓ 三阶段计算支持
  - 初期、近期、远期三个发展阶段
  - 每个阶段独立的客流数据和计算参数
  - 支持不同阶段的高峰系数设置

✓ 数据库存储系统
  - 支持项目保存、读取、删除
  - 数据覆盖保护和另存为功能
  - 项目搜索功能

✓ Excel导出功能
  - 单项目导出为Excel文件
  - 批量导出多个项目
  - 完全按照标准格式导出
  - 每个项目作为独立工作表

✓ 增强的用户界面
  - 表格式数据输入界面
  - 紧凑的界面设计
  - 直观的结果显示

═══════════════════════════════════════════════════════════════

🎯 界面布局

顶部按钮区：
• 计算 - 执行所有阶段的计算
• 重置 - 清空所有数据并恢复默认值
• 保存数据 - 保存当前项目到数据库
• 读取数据 - 从数据库读取已保存的项目
• 导出Excel - 导出当前项目为Excel文件
• 批量导出 - 导出所有项目为Excel文件
• 帮助 - 显示本帮助文档

客流数据区域：
表格式输入界面，包含三个阶段的上下行客流数据
- 上行数据：上客、下客、断面
- 下行数据：上客、下客、断面

计算参数区域：
每个阶段可设置不同值

中间计算值区域：
显示每个阶段的中间计算结果

最终计算结果区域：
显示每个阶段的最终结果（站台宽度、疏散人数、疏散时间）

═══════════════════════════════════════════════════════════════

📝 基本操作流程

新建项目：
1. 启动程序
2. 输入各阶段的客流数据
3. 调整计算参数（如需要）
4. 点击"计算"按钮
5. 点击"保存数据"，输入站点名称

读取项目：
1. 点击"读取数据"按钮
2. 在项目列表中选择目标项目
3. 双击或点击"加载"按钮
4. 数据自动填入界面

修改项目：
1. 读取已有项目
2. 修改数据
3. 重新计算
4. 保存时选择"覆盖"或"另存为"

导出数据：
1. 确保项目已保存
2. 点击"导出Excel"导出当前项目
3. 或点击"批量导出"导出所有项目

═══════════════════════════════════════════════════════════════

🧮 计算公式

根据《地铁设计防火标准》（GB 51298-2018）5.1.2条编制。
5.1.2 乘客全部撤离站台的时间应满足下式要求:

式中：

Q1——远期或客流控制期中超高峰小时最大客流量时一列进站列车的载客人数(人)；

Q2——远期或客流控制期中超高峰小时站台上的最大候车乘客人数(人)；

A1——一台自动扶梯的通过能力[人／(min·台)]；

A2——单位宽度疏散楼梯的通过能力[人／(min·m)]；

N——用作疏散的自动扶梯的数量(台)；

B——疏散楼梯的总宽度(m)(每组楼梯的宽度应按0.55m的整倍数计算)。

站台宽度计算：
b1 = (上行单侧上下客合计 × 高峰系数 ÷ 列车对数 × 站台人流密度 ÷ 站台长度) + 站台边缘距离
b2 = (下行单侧上下客合计 × 高峰系数 ÷ 列车对数 × 站台人流密度 ÷ 站台长度) + 站台边缘距离

═══════════════════════════════════════════════════════════════

💾 数据库功能

项目管理：
• 保存：将当前数据保存为新项目或覆盖现有项目
• 读取：从数据库加载已保存的项目
• 搜索：按站点名称搜索项目
• 删除：删除不需要的项目

数据保护：
• 数据修改检测：修改数据后会提示保存选项
• 覆盖保护：覆盖现有项目时会确认
• 另存为：可将修改后的数据保存为新项目

═══════════════════════════════════════════════════════════════

📊 Excel导出功能

导出格式：
完全按照标准格式导出，包含：
- 客流数据表格
- 计算参数表格
- 中间计算值表格
- 最终计算结果表格

导出选项：
1. 单项目导出：导出当前项目为单个Excel文件
2. 批量导出：将所有项目导出到一个Excel文件，每个项目作为独立工作表

═══════════════════════════════════════════════════════════════

版本信息：V1.0.0 (2025-08-01)
专业、准确、易用的地铁站台疏散分析工具
版权所属：山东轨道交通勘察设计院有限公司
"""


def main():
    """主函数"""
    root = tk.Tk()
    app = MetroEvacuationGUIV2(root)
    root.mainloop()


if __name__ == "__main__":
    main()
