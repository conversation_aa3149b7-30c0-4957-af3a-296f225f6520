#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
地铁站台疏散时间计算器 GUI V1.0
支持数据库存储、Excel导出等功能
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import tkinter.font as tkFont
from metro_evacuation_calculator_v2 import MetroEvacuationCalculatorV2
from database_manager import DatabaseManager
from excel_exporter import ExcelExporter
import os
import shutil
from datetime import datetime
import threading
import time
# 移除PIL依赖，使用tkinter内置图片支持

class MetroEvacuationGUIV2:
    """地铁站台疏散时间计算器GUI V1.0"""
    
    def __init__(self, root):
        self.root = root
        self.root.title("地铁站台疏散时间计算器 V1.0")
        self.root.geometry("1200x650")  # 进一步减小宽度和高度，界面更紧凑
        self.root.resizable(False, False)  # 禁止调整窗口大小
        
        # 创建计算器和管理器实例
        self.calculator = MetroEvacuationCalculatorV2()
        self.db_manager = DatabaseManager("data.db")  # 指定固定的数据库文件
        self.excel_exporter = ExcelExporter()
        
        # 当前项目信息
        self.current_station_name = None
        self.is_data_modified = False
        self.last_saved_data = None  # 用于检测数据变化

        # 数据保护设置
        self.auto_backup_enabled = True
        self.backup_interval = 1800  # 30分钟自动备份一次
        self.max_backup_files = 10  # 最多保留10个备份文件

        # 设置字体
        self.font_title = tkFont.Font(family="微软雅黑", size=14, weight="bold")
        self.font_label = tkFont.Font(family="微软雅黑", size=10)
        self.font_entry = tkFont.Font(family="微软雅黑", size=9)

        self.create_widgets()
        self.load_default_values()

        # 设置窗口关闭事件处理
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

        # 启动自动备份
        if self.auto_backup_enabled:
            self.start_auto_backup()
    
    def create_widgets(self):
        """创建界面组件"""
        # 主标题
        title_label = tk.Label(self.root, text="地铁站台疏散时间计算器 V1.0", 
                              font=self.font_title, fg="blue")
        title_label.pack(pady=5)
        
        # 创建主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # 创建顶部按钮区域
        self.create_button_section(main_frame)
        
        # 创建滚动区域
        self.create_scrollable_area(main_frame)
    
    def create_button_section(self, parent):
        """创建按钮区域"""
        button_frame = ttk.Frame(parent)
        button_frame.pack(fill=tk.X, pady=(0, 10))

        # 所有按钮紧贴左侧排列
        buttons_container = ttk.Frame(button_frame)
        buttons_container.pack(side=tk.LEFT, anchor='w')

        # 获取程序背景色
        bg_color = self.root.cget('bg')

        # 按钮样式配置 - 减小按钮尺寸
        button_config = {
            'font': ('微软雅黑', 10, 'bold'),  # 适中的字体大小
            'width': 8,   # 减小宽度
            'height': 1,  # 减小高度
            'relief': 'raised',
            'bd': 1,      # 减小边框
            'bg': bg_color,  # 使用程序背景色
            'fg': 'black'    # 黑色文字
        }

        # 计算按钮
        calc_button = tk.Button(buttons_container, text="计算",
                               command=self.calculate, **button_config)
        calc_button.pack(side=tk.LEFT, padx=3)

        # 重置按钮
        reset_button = tk.Button(buttons_container, text="重置",
                                command=self.reset_values, **button_config)
        reset_button.pack(side=tk.LEFT, padx=3)

        # 保存数据按钮
        save_button = tk.Button(buttons_container, text="保存数据",
                               command=self.save_data, **button_config)
        save_button.pack(side=tk.LEFT, padx=3)

        # 读取数据按钮
        load_button = tk.Button(buttons_container, text="读取数据",
                               command=self.load_data, **button_config)
        load_button.pack(side=tk.LEFT, padx=3)

        # 导出Excel按钮
        export_button = tk.Button(buttons_container, text="导出Excel",
                                 command=self.export_excel, **button_config)
        export_button.pack(side=tk.LEFT, padx=3)

        # 批量导出按钮
        batch_export_button = tk.Button(buttons_container, text="批量导出",
                                       command=self.batch_export_excel, **button_config)
        batch_export_button.pack(side=tk.LEFT, padx=3)

        # 备份按钮
        backup_button = tk.Button(buttons_container, text="备份",
                                 command=self.show_backup_menu, **button_config)
        backup_button.pack(side=tk.LEFT, padx=3)

        # 帮助按钮
        help_button = tk.Button(buttons_container, text="帮助",
                               command=self.show_help, **button_config)
        help_button.pack(side=tk.LEFT, padx=3)
    
    def create_scrollable_area(self, parent):
        """创建左右分栏的内容区域"""
        # 创建主容器
        main_container = ttk.Frame(parent)
        main_container.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 左侧区域
        left_frame = ttk.Frame(main_container)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))

        # 右侧区域
        right_frame = ttk.Frame(main_container)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(5, 0))

        # 在左右区域中创建内容
        self.create_left_content(left_frame)
        self.create_right_content(right_frame)
    
    def create_left_content(self, parent):
        """创建左侧内容区域"""
        # 客流数据区域
        self.create_passenger_flow_section(parent)

        # 计算参数区域
        self.create_parameters_section(parent)

    def create_right_content(self, parent):
        """创建右侧内容区域"""
        # 中间计算值区域
        self.create_intermediate_section(parent)

        # 最终结果区域
        self.create_results_section(parent)

        # 软件信息图片区域
        self.create_software_info_section(parent)
    
    def create_passenger_flow_section(self, parent):
        """创建客流数据区域"""
        # 客流数据组
        passenger_group = ttk.LabelFrame(parent, text="客流数据（人）", padding=10)
        passenger_group.pack(fill=tk.X, pady=(0, 10))
        
        # 创建表格样式的布局
        # 表头
        headers = ["", "上行", "", "", "下行", "", ""]
        sub_headers = ["", "上客", "下客", "断面", "上客", "下客", "断面"]
        
        # 第一行表头
        for col, header in enumerate(headers):
            if header:
                if header == "上行":
                    label = tk.Label(passenger_group, text=header, font=self.font_label, bg="lightgray")
                    label.grid(row=0, column=col, columnspan=3, sticky="ew", padx=1, pady=1)
                elif header == "下行":
                    label = tk.Label(passenger_group, text=header, font=self.font_label, bg="lightgray")
                    label.grid(row=0, column=col, columnspan=3, sticky="ew", padx=1, pady=1)
        
        # 第二行表头
        for col, header in enumerate(sub_headers):
            if header:
                label = tk.Label(passenger_group, text=header, font=self.font_label, bg="lightblue")
                label.grid(row=1, column=col, sticky="ew", padx=1, pady=1)
        
        # 数据行
        self.passenger_entries = {}
        periods = [("初期", "initial"), ("近期", "near"), ("远期", "far")]
        
        for row, (period_name, period_key) in enumerate(periods, 2):
            # 阶段名称
            label = tk.Label(passenger_group, text=period_name, font=self.font_label)
            label.grid(row=row, column=0, sticky="w", padx=5, pady=2)
            
            # 输入框
            entries = {}
            flow_types = ['up_in', 'up_out', 'up_area', 'down_in', 'down_out', 'down_area']
            for col, flow_type in enumerate(flow_types, 1):
                entry = tk.Entry(passenger_group, font=self.font_entry, width=10, justify='center')
                entry.grid(row=row, column=col, padx=1, pady=2)
                entry.bind('<KeyRelease>', self.on_data_changed)
                entries[flow_type] = entry
            
            self.passenger_entries[period_key] = entries
        
        # 设置列权重
        for col in range(7):
            passenger_group.columnconfigure(col, weight=1)
    
    def create_parameters_section(self, parent):
        """创建计算参数区域"""
        param_group = ttk.LabelFrame(parent, text="计算参数", padding=10)
        param_group.pack(fill=tk.X, pady=(0, 10))
        
        # 表头
        headers = ["", "初期", "近期", "远期", "单位"]
        for col, header in enumerate(headers):
            label = tk.Label(param_group, text=header, font=self.font_label, bg="lightgray")
            label.grid(row=0, column=col, sticky="ew", padx=1, pady=1)
        
        # 参数列表 - 现在所有参数都支持三个阶段
        self.param_entries = {}
        params = [
            ("高峰系数", "k", "/"),
            ("列车对数", "n_train", "/"),
            ("站台人流密度ρ", "p", "m²/人"),
            ("站台长度L", "l", "m"),
            ("站台边缘到列车全部停稳内侧距离M", "m", "m"),
            ("一台自动扶梯的通过能力A1", "a_1", "人/h·m"),
            ("疏散楼梯的通过能力A2", "a_2", "人/h·m"),
            ("自动扶梯台数N", "n_auto", "/"),
            ("疏散楼梯宽度B", "b", "m"),
            ("疏散楼梯数量", "n_stair", "/")
        ]
        
        for row, (param_name, param_key, unit) in enumerate(params, 1):
            # 参数名称
            label = tk.Label(param_group, text=param_name, font=self.font_label)
            label.grid(row=row, column=0, sticky="w", padx=5, pady=2)

            # 所有参数都支持三个阶段
            entries = {}
            for col, period in enumerate(['initial', 'near', 'far'], 1):
                entry = tk.Entry(param_group, font=self.font_entry, width=10, justify='center')
                entry.grid(row=row, column=col, padx=1, pady=2)
                entry.bind('<KeyRelease>', self.on_data_changed)
                entries[period] = entry
            self.param_entries[param_key] = entries

            # 单位
            label = tk.Label(param_group, text=unit, font=self.font_label)
            label.grid(row=row, column=4, sticky="w", padx=5, pady=2)
        
        # 设置列权重
        for col in range(5):
            param_group.columnconfigure(col, weight=1)
    
    def on_data_changed(self, event=None):
        """数据变化时的回调"""
        self.is_data_modified = True
        # 更新窗口标题显示修改状态
        self.update_window_title()

    def update_window_title(self):
        """更新窗口标题"""
        base_title = "地铁站台疏散时间计算器 V1.0"
        if self.current_station_name:
            title = f"{base_title} - {self.current_station_name}"
            if self.is_data_modified:
                title += " *"
        else:
            title = base_title
            if self.is_data_modified:
                title += " *"
        self.root.title(title)

    def on_closing(self):
        """窗口关闭事件处理"""
        if self.is_data_modified:
            result = messagebox.askyesnocancel(
                "退出确认",
                "当前有未保存的数据修改。\n\n"
                "是：保存后退出\n"
                "否：不保存直接退出\n"
                "取消：返回程序"
            )

            if result is None:  # 取消
                return
            elif result:  # 是，保存后退出
                self.save_data()
                if self.is_data_modified:  # 如果保存失败或用户取消保存
                    return

        # 停止自动备份
        if hasattr(self, 'backup_thread') and self.backup_thread.is_alive():
            self.backup_running = False

        self.root.destroy()

    def start_auto_backup(self):
        """启动自动备份线程"""
        self.backup_running = True
        self.backup_thread = threading.Thread(target=self._auto_backup_worker, daemon=True)
        self.backup_thread.start()

    def _auto_backup_worker(self):
        """自动备份工作线程"""
        while self.backup_running:
            try:
                time.sleep(self.backup_interval)
                if self.backup_running:  # 再次检查，防止在sleep期间被停止
                    self.create_database_backup()
            except Exception as e:
                print(f"自动备份出错: {e}")

    def create_database_backup(self):
        """创建数据库备份"""
        try:
            if not os.path.exists(self.db_manager.db_path):
                return

            # 创建备份目录
            backup_dir = "backups"
            if not os.path.exists(backup_dir):
                os.makedirs(backup_dir)

            # 生成备份文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"data_backup_{timestamp}.db"
            backup_path = os.path.join(backup_dir, backup_filename)

            # 复制数据库文件
            shutil.copy2(self.db_manager.db_path, backup_path)

            # 清理旧备份文件
            self.cleanup_old_backups(backup_dir)

            print(f"数据库备份已创建: {backup_path}")

        except Exception as e:
            print(f"创建备份失败: {e}")

    def cleanup_old_backups(self, backup_dir):
        """清理旧的备份文件"""
        try:
            # 获取所有备份文件
            backup_files = []
            for filename in os.listdir(backup_dir):
                if filename.startswith("data_backup_") and filename.endswith(".db"):
                    filepath = os.path.join(backup_dir, filename)
                    backup_files.append((filepath, os.path.getmtime(filepath)))

            # 按修改时间排序，保留最新的文件
            backup_files.sort(key=lambda x: x[1], reverse=True)

            # 删除超出数量限制的文件
            for filepath, _ in backup_files[self.max_backup_files:]:
                os.remove(filepath)
                print(f"已删除旧备份: {filepath}")

        except Exception as e:
            print(f"清理备份文件失败: {e}")

    def manual_backup(self):
        """手动备份数据库"""
        try:
            if not os.path.exists(self.db_manager.db_path):
                messagebox.showwarning("备份失败", "数据库文件不存在！")
                return

            # 让用户选择备份位置
            backup_path = filedialog.asksaveasfilename(
                title="选择备份保存位置",
                defaultextension=".db",
                filetypes=[("数据库文件", "*.db"), ("所有文件", "*.*")],
                initialfile=f"data_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
            )

            if backup_path:
                shutil.copy2(self.db_manager.db_path, backup_path)
                messagebox.showinfo("备份成功", f"数据库已备份到:\n{backup_path}")

        except Exception as e:
            messagebox.showerror("备份失败", f"备份过程中出现错误：{str(e)}")

    def restore_from_backup(self):
        """从备份恢复数据库"""
        try:
            # 选择备份文件
            backup_path = filedialog.askopenfilename(
                title="选择要恢复的备份文件",
                filetypes=[("数据库文件", "*.db"), ("所有文件", "*.*")]
            )

            if backup_path:
                result = messagebox.askyesno(
                    "恢复确认",
                    "恢复备份将覆盖当前数据库中的所有数据。\n\n"
                    "确定要继续吗？"
                )

                if result:
                    # 先备份当前数据库
                    current_backup = f"data_before_restore_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
                    shutil.copy2(self.db_manager.db_path, current_backup)

                    # 恢复备份
                    shutil.copy2(backup_path, self.db_manager.db_path)

                    messagebox.showinfo(
                        "恢复成功",
                        f"数据库已从备份恢复！\n\n"
                        f"原数据库已备份为：{current_backup}\n"
                        f"请重启程序以加载恢复的数据。"
                    )

        except Exception as e:
            messagebox.showerror("恢复失败", f"恢复过程中出现错误：{str(e)}")

    def show_backup_menu(self):
        """显示备份管理菜单"""
        menu = tk.Menu(self.root, tearoff=0)
        menu.add_command(label="手动备份", command=self.manual_backup)
        menu.add_command(label="从备份恢复", command=self.restore_from_backup)
        menu.add_separator()
        menu.add_command(label="查看备份文件夹", command=self.open_backup_folder)
        menu.add_command(label="清理旧备份", command=self.cleanup_old_backups_manual)

        # 在鼠标位置显示菜单
        try:
            menu.tk_popup(self.root.winfo_pointerx(), self.root.winfo_pointery())
        finally:
            menu.grab_release()

    def open_backup_folder(self):
        """打开备份文件夹"""
        backup_dir = "backups"
        if not os.path.exists(backup_dir):
            os.makedirs(backup_dir)

        try:
            if os.name == 'nt':  # Windows
                os.startfile(backup_dir)
            elif os.name == 'posix':  # macOS and Linux
                os.system(f'open "{backup_dir}"')
        except Exception as e:
            messagebox.showerror("错误", f"无法打开备份文件夹：{str(e)}")

    def cleanup_old_backups_manual(self):
        """手动清理旧备份"""
        backup_dir = "backups"
        if not os.path.exists(backup_dir):
            messagebox.showinfo("提示", "备份文件夹不存在！")
            return

        try:
            # 获取所有备份文件
            backup_files = []
            for filename in os.listdir(backup_dir):
                if filename.startswith("data_backup_") and filename.endswith(".db"):
                    filepath = os.path.join(backup_dir, filename)
                    backup_files.append((filepath, os.path.getmtime(filepath)))

            if len(backup_files) <= self.max_backup_files:
                messagebox.showinfo("提示", f"当前备份文件数量（{len(backup_files)}）未超过限制（{self.max_backup_files}），无需清理。")
                return

            # 按修改时间排序，保留最新的文件
            backup_files.sort(key=lambda x: x[1], reverse=True)
            files_to_delete = backup_files[self.max_backup_files:]

            if files_to_delete:
                result = messagebox.askyesno(
                    "清理确认",
                    f"将删除 {len(files_to_delete)} 个旧备份文件，保留最新的 {self.max_backup_files} 个。\n\n"
                    "确定要继续吗？"
                )

                if result:
                    deleted_count = 0
                    for filepath, _ in files_to_delete:
                        try:
                            os.remove(filepath)
                            deleted_count += 1
                        except Exception as e:
                            print(f"删除文件失败 {filepath}: {e}")

                    messagebox.showinfo("清理完成", f"已删除 {deleted_count} 个旧备份文件。")

        except Exception as e:
            messagebox.showerror("清理失败", f"清理过程中出现错误：{str(e)}")

    def create_intermediate_section(self, parent):
        """创建中间计算值区域"""
        intermediate_group = ttk.LabelFrame(parent, text="中间计算值", padding=10)
        intermediate_group.pack(fill=tk.X, pady=(0, 10))

        # 表头
        headers = ["", "初期", "近期", "远期", "单位"]
        for col, header in enumerate(headers):
            label = tk.Label(intermediate_group, text=header, font=self.font_label, bg="lightgray")
            label.grid(row=0, column=col, sticky="ew", padx=1, pady=1)

        # 中间计算值
        self.intermediate_labels = {}
        intermediate_values = [
            ("单侧上下客合计（上行）", "up_add", "人"),
            ("单侧上下客合计（下行）", "down_add", "人"),
            ("上客流量合计", "in_add", "人")
        ]

        for row, (value_name, value_key, unit) in enumerate(intermediate_values, 1):
            # 名称
            label = tk.Label(intermediate_group, text=value_name, font=self.font_label)
            label.grid(row=row, column=0, sticky="w", padx=5, pady=2)

            # 结果显示
            labels = {}
            for col, period in enumerate(['initial', 'near', 'far'], 1):
                result_label = tk.Label(intermediate_group, text="0.00", font=self.font_entry,
                                      bg="white", relief="sunken", width=12)
                result_label.grid(row=row, column=col, padx=1, pady=2)
                labels[period] = result_label

            self.intermediate_labels[value_key] = labels

            # 单位
            label = tk.Label(intermediate_group, text=unit, font=self.font_label)
            label.grid(row=row, column=4, sticky="w", padx=5, pady=2)

        # 设置列权重
        for col in range(5):
            intermediate_group.columnconfigure(col, weight=1)

    def create_results_section(self, parent):
        """创建最终结果区域"""
        results_group = ttk.LabelFrame(parent, text="最终计算结果", padding=10)
        results_group.pack(fill=tk.X, pady=(0, 10))

        # 表头
        headers = ["", "初期", "近期", "远期", "单位"]
        for col, header in enumerate(headers):
            label = tk.Label(results_group, text=header, font=self.font_label, bg="lightgray")
            label.grid(row=0, column=col, sticky="ew", padx=1, pady=1)

        # 最终结果
        self.result_labels = {}
        final_results = [
            ("站台宽度b1", "b_1", "m"),
            ("站台宽度b2", "b_2", "m"),
            ("Q1", "q_1", "人"),
            ("Q2", "q_2", "人"),
            ("疏散时间T", "t", "min")
        ]

        for row, (result_name, result_key, unit) in enumerate(final_results, 1):
            # 名称
            label = tk.Label(results_group, text=result_name, font=self.font_label)
            label.grid(row=row, column=0, sticky="w", padx=5, pady=2)

            # 结果显示
            labels = {}
            for col, period in enumerate(['initial', 'near', 'far'], 1):
                result_label = tk.Label(results_group, text="0.000000", font=self.font_entry,
                                      bg="lightyellow", relief="sunken", width=12)
                result_label.grid(row=row, column=col, padx=1, pady=2)
                labels[period] = result_label

            self.result_labels[result_key] = labels

            # 单位
            label = tk.Label(results_group, text=unit, font=self.font_label)
            label.grid(row=row, column=4, sticky="w", padx=5, pady=2)

        # 设置列权重
        for col in range(5):
            results_group.columnconfigure(col, weight=1)

    def create_software_info_section(self, parent):
        """创建软件信息图片区域"""
        try:
            # 检查图片文件是否存在
            image_path = "软件信息.png"
            if not os.path.exists(image_path):
                print(f"图片文件不存在: {image_path}")
                return

            # 创建图片显示区域
            info_frame = ttk.Frame(parent)
            info_frame.pack(fill=tk.X, pady=(10, 0))

            try:
                # 使用tkinter内置的PhotoImage加载PNG图片
                # 注意：tkinter的PhotoImage支持PNG和GIF格式
                self.software_info_image = tk.PhotoImage(file=image_path)

                # 获取原始图片尺寸
                original_width = self.software_info_image.width()
                original_height = self.software_info_image.height()

                # 设置显示尺寸（增加图片大小）
                display_width = 350   # 增加宽度
                display_height = 180  # 增加高度

                # 如果原图比目标尺寸小，则使用原图尺寸
                if original_width <= display_width and original_height <= display_height:
                    final_image = self.software_info_image
                    print(f"使用原始尺寸显示图片: {original_width}x{original_height}")
                else:
                    # 如果原图较大，进行简单的子采样缩放
                    # 计算缩放因子
                    scale_x = max(1, original_width // display_width)
                    scale_y = max(1, original_height // display_height)
                    scale_factor = max(scale_x, scale_y)

                    # 使用subsample方法缩放（tkinter内置方法）
                    final_image = self.software_info_image.subsample(scale_factor, scale_factor)

                    actual_width = original_width // scale_factor
                    actual_height = original_height // scale_factor
                    print(f"缩放后显示图片: {actual_width}x{actual_height} (缩放因子: {scale_factor})")

                # 创建图片标签，直接显示，不添加边框效果
                image_label = tk.Label(info_frame, image=final_image)
                image_label.pack(anchor=tk.E, padx=(0, 10), pady=5)  # 右对齐

                # 保存图片引用，防止被垃圾回收
                self.software_info_image_final = final_image

                print(f"成功加载软件信息图片: {image_path}")

            except tk.TclError as e:
                print(f"tkinter加载图片失败: {e}")
                print("提示：tkinter的PhotoImage只支持PNG和GIF格式")
                self.create_fallback_info_display(info_frame)
            except Exception as e:
                print(f"加载图片时出现其他错误: {e}")
                self.create_fallback_info_display(info_frame)

        except Exception as e:
            print(f"创建软件信息区域失败: {e}")

    def create_fallback_info_display(self, parent):
        """创建备用的软件信息显示"""
        # 创建简洁的文本信息作为备用
        info_text = "软件信息图片\n(加载失败)"

        info_label = tk.Label(
            parent,
            text=info_text,
            font=("微软雅黑", 10),
            fg="gray",
            width=20,
            height=3,
            justify=tk.CENTER
        )
        info_label.pack(anchor=tk.E, padx=(0, 10), pady=5)  # 右对齐

    def load_default_values(self):
        """加载默认值"""
        # 设置默认的计算参数 - 所有参数都支持三个阶段
        defaults = {
            'k': {'initial': '1.13', 'near': '1.13', 'far': '1.13'},
            'n_train': {'initial': '20', 'near': '20', 'far': '20'},
            'p': {'initial': '0.5', 'near': '0.5', 'far': '0.5'},
            'l': {'initial': '135.6', 'near': '135.6', 'far': '135.6'},
            'm': {'initial': '0.3', 'near': '0.3', 'far': '0.3'},
            'a_1': {'initial': '7300', 'near': '7300', 'far': '7300'},
            'a_2': {'initial': '3700', 'near': '3700', 'far': '3700'},
            'n_auto': {'initial': '2', 'near': '2', 'far': '2'},
            'b': {'initial': '3.3', 'near': '3.3', 'far': '3.3'},
            'n_stair': {'initial': '1', 'near': '1', 'far': '1'}
        }

        for key, periods in defaults.items():
            if key in self.param_entries:
                for period, val in periods.items():
                    self.param_entries[key][period].insert(0, val)



    def clear_all_entries(self):
        """清空所有输入框"""
        # 清空客流数据
        for period_entries in self.passenger_entries.values():
            for entry in period_entries.values():
                entry.delete(0, tk.END)

        # 清空参数 - 现在所有参数都是字典形式
        for param_entry in self.param_entries.values():
            for entry in param_entry.values():
                entry.delete(0, tk.END)

    def reset_values(self):
        """重置所有值"""
        # 检查是否有未保存的数据
        if self.is_data_modified:
            result = messagebox.askyesnocancel(
                "重置确认",
                "当前有未保存的数据修改。\n\n"
                "是：保存后重置\n"
                "否：不保存直接重置\n"
                "取消：返回"
            )

            if result is None:  # 取消
                return
            elif result:  # 是，保存后重置
                self.save_data()
                if self.is_data_modified:  # 如果保存失败或用户取消保存
                    return

        self.clear_all_entries()
        self.load_default_values()

        # 清空结果显示
        for period_labels in self.intermediate_labels.values():
            for label in period_labels.values():
                label.config(text="0")  # 中间计算值显示整数

        for period_labels in self.result_labels.values():
            for label in period_labels.values():
                label.config(text="0.000")  # 最终结果显示3位小数

        self.current_station_name = None
        self.is_data_modified = False
        self.last_saved_data = None
        self.update_window_title()

    def get_input_values(self):
        """获取输入值"""
        try:
            # 获取客流数据
            passenger_data = {}
            for period, entries in self.passenger_entries.items():
                period_data = {}
                for key, entry in entries.items():
                    value = entry.get().strip()
                    if not value:
                        value = "0"
                    period_data[key] = float(value)
                passenger_data[period] = period_data

            # 获取计算参数 - 现在所有参数都支持三个阶段
            param_data = {}
            for key, period_entries in self.param_entries.items():
                for period, entry in period_entries.items():
                    value = entry.get().strip()
                    if not value:
                        # 设置默认值
                        if key == "k":
                            value = "1.13"
                        elif key in ["n_train", "n_auto", "n_stair"]:
                            value = "20" if key == "n_train" else ("2" if key == "n_auto" else "1")
                        elif key in ["p", "l", "m", "a_1", "a_2", "b"]:
                            defaults = {"p": "0.5", "l": "135.6", "m": "0.3",
                                      "a_1": "7300", "a_2": "3700", "b": "3.3"}
                            value = defaults.get(key, "0")
                        else:
                            value = "0"
                    param_data[f'{key}_{period}'] = float(value)

            return passenger_data, param_data

        except ValueError as e:
            messagebox.showerror("输入错误", f"请检查输入数据格式: {str(e)}")
            return None, None

    def calculate(self):
        """执行计算"""
        passenger_data, param_data = self.get_input_values()
        if passenger_data is None or param_data is None:
            return

        try:
            # 设置客流数据
            for period, data in passenger_data.items():
                self.calculator.set_passenger_flow(
                    period, data['up_in'], data['up_out'], data['up_area'],
                    data['down_in'], data['down_out'], data['down_area']
                )

            # 设置计算参数
            self.calculator.set_parameters(**param_data)

            # 执行计算
            self.calculator.calculate_all_periods()

            # 更新结果显示
            self.update_results()

            messagebox.showinfo("计算完成", "所有阶段计算完成！")

        except Exception as e:
            messagebox.showerror("计算错误", f"计算过程中出现错误：{str(e)}")

    def update_results(self):
        """更新结果显示"""
        periods = ['initial', 'near', 'far']

        # 更新中间计算值 - 保留整数
        for value_key, period_labels in self.intermediate_labels.items():
            for period in periods:
                result = self.calculator.get_period_results(period)
                period_labels[period].config(text=f"{result[value_key]:.0f}")

        # 更新最终结果 - 保留3位小数
        for result_key, period_labels in self.result_labels.items():
            for period in periods:
                result = self.calculator.get_period_results(period)
                period_labels[period].config(text=f"{result[result_key]:.3f}")

    def save_data(self):
        """保存数据"""
        # 检查是否有数据需要保存
        passenger_data, param_data = self.get_input_values()
        if passenger_data is None or param_data is None:
            return

        # 如果当前有项目且数据已修改，询问是否覆盖
        if self.current_station_name and self.is_data_modified:
            result = messagebox.askyesnocancel(
                "保存确认",
                f"当前项目 '{self.current_station_name}' 的数据已修改。\n\n"
                "是：另存为新项目\n"
                "否：覆盖当前项目\n"
                "取消：不保存"
            )

            if result is None:  # 取消
                return
            elif result:  # 是，另存为
                station_name = self.get_station_name(is_save_as=True)
                if not station_name:
                    return
            else:  # 否，覆盖
                station_name = self.current_station_name
        else:
            # 新项目或未修改，直接输入站名
            station_name = self.get_station_name(is_save_as=False)
            if not station_name:
                return

        # 获取所有数据
        all_data = self.calculator.get_all_data()
        all_data['station_name'] = station_name

        # 保存到数据库
        if self.db_manager.save_project(station_name, all_data):
            messagebox.showinfo("保存成功", f"项目 '{station_name}' 保存成功！")
            self.current_station_name = station_name
            self.is_data_modified = False
            self.last_saved_data = self.get_current_data_snapshot()
            self.update_window_title()
        else:
            messagebox.showerror("保存失败", "保存数据时出现错误！")

    def get_station_name(self, is_save_as=False):
        """获取站点名称"""
        dialog = tk.Toplevel(self.root)
        title = "另存为新项目" if is_save_as else "输入站点名称"
        dialog.title(title)
        dialog.geometry("400x200")
        dialog.transient(self.root)
        dialog.grab_set()

        # 居中显示
        dialog.geometry("+%d+%d" % (self.root.winfo_rootx() + 50, self.root.winfo_rooty() + 50))

        station_name = tk.StringVar()

        # 如果是另存为，提供默认名称建议
        if is_save_as and self.current_station_name:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M")
            default_name = f"{self.current_station_name}_副本_{timestamp}"
            station_name.set(default_name)

        tk.Label(dialog, text=f"请输入站点名称:", font=self.font_label).pack(pady=10)

        if is_save_as:
            tk.Label(dialog, text="(将创建当前项目的副本)",
                    font=self.font_label, fg="gray").pack(pady=(0, 10))

        entry = tk.Entry(dialog, textvariable=station_name, font=self.font_entry, width=40)
        entry.pack(pady=10)
        entry.focus()

        # 如果有默认名称，选中文本
        if station_name.get():
            entry.select_range(0, tk.END)

        # 检查名称是否已存在的提示标签
        status_label = tk.Label(dialog, text="", font=self.font_label, fg="red")
        status_label.pack(pady=5)

        def check_name_exists():
            """检查名称是否已存在"""
            name = station_name.get().strip()
            if name:
                projects = self.db_manager.get_all_projects()
                existing_names = [proj[0] for proj in projects]
                if name in existing_names:
                    status_label.config(text="⚠ 该名称已存在，保存将覆盖现有项目", fg="orange")
                else:
                    status_label.config(text="✓ 名称可用", fg="green")
            else:
                status_label.config(text="", fg="black")

        # 绑定名称变化事件
        station_name.trace('w', lambda *args: check_name_exists())

        button_frame = tk.Frame(dialog)
        button_frame.pack(pady=10)

        result = [None]

        def on_ok():
            name = station_name.get().strip()
            if name:
                result[0] = name
                dialog.destroy()
            else:
                messagebox.showerror("错误", "请输入有效的站点名称！")

        def on_cancel():
            dialog.destroy()

        tk.Button(button_frame, text="确定", command=on_ok).pack(side=tk.LEFT, padx=5)
        tk.Button(button_frame, text="取消", command=on_cancel).pack(side=tk.LEFT, padx=5)

        # 绑定回车键
        entry.bind('<Return>', lambda event: on_ok())

        dialog.wait_window()
        return result[0]

    def load_data(self):
        """读取数据"""
        # 检查是否有未保存的数据
        if self.is_data_modified:
            result = messagebox.askyesnocancel(
                "加载确认",
                "当前有未保存的数据修改。\n\n"
                "是：保存后加载新数据\n"
                "否：不保存直接加载\n"
                "取消：返回"
            )

            if result is None:  # 取消
                return
            elif result:  # 是，保存后加载
                self.save_data()
                if self.is_data_modified:  # 如果保存失败或用户取消保存
                    return

        # 创建项目选择对话框
        self.show_project_selector()

    def show_project_selector(self):
        """显示项目选择器"""
        dialog = tk.Toplevel(self.root)
        dialog.title("选择项目")
        dialog.geometry("600x400")
        dialog.transient(self.root)
        dialog.grab_set()

        # 居中显示
        dialog.geometry("+%d+%d" % (self.root.winfo_rootx() + 100, self.root.winfo_rooty() + 100))

        # 搜索框
        search_frame = tk.Frame(dialog)
        search_frame.pack(fill=tk.X, padx=10, pady=5)

        tk.Label(search_frame, text="搜索:", font=self.font_label).pack(side=tk.LEFT)
        search_var = tk.StringVar()
        search_entry = tk.Entry(search_frame, textvariable=search_var, font=self.font_entry)
        search_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)

        def on_search():
            keyword = search_var.get().strip()
            if keyword:
                projects = self.db_manager.search_projects(keyword)
            else:
                projects = self.db_manager.get_all_projects()
            update_project_list(projects)

        tk.Button(search_frame, text="搜索", command=on_search).pack(side=tk.RIGHT)

        # 项目列表
        list_frame = tk.Frame(dialog)
        list_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # 创建Treeview
        columns = ('station_name', 'update_time')
        tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=15)
        tree.heading('station_name', text='站点名称')
        tree.heading('update_time', text='更新时间')
        tree.column('station_name', width=200)
        tree.column('update_time', width=200)

        # 滚动条
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=tree.yview)
        tree.configure(yscrollcommand=scrollbar.set)

        tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        def update_project_list(projects):
            """更新项目列表"""
            tree.delete(*tree.get_children())
            for station_name, update_time in projects:
                tree.insert('', tk.END, values=(station_name, update_time))

        # 初始加载所有项目
        projects = self.db_manager.get_all_projects()
        update_project_list(projects)

        # 按钮框
        button_frame = tk.Frame(dialog)
        button_frame.pack(fill=tk.X, padx=10, pady=5)

        def on_load():
            selection = tree.selection()
            if selection:
                item = tree.item(selection[0])
                station_name = item['values'][0]
                self.load_project_data(station_name)
                dialog.destroy()
            else:
                messagebox.showwarning("提示", "请选择一个项目！")

        def on_delete():
            selection = tree.selection()
            if selection:
                item = tree.item(selection[0])
                station_name = item['values'][0]
                if messagebox.askyesno("确认删除", f"确定要删除项目 '{station_name}' 吗？"):
                    if self.db_manager.delete_project(station_name):
                        messagebox.showinfo("删除成功", f"项目 '{station_name}' 已删除！")
                        # 刷新列表
                        projects = self.db_manager.get_all_projects()
                        update_project_list(projects)
                    else:
                        messagebox.showerror("删除失败", "删除项目时出现错误！")
            else:
                messagebox.showwarning("提示", "请选择一个项目！")

        tk.Button(button_frame, text="加载", command=on_load, bg="lightgreen").pack(side=tk.LEFT, padx=5)
        tk.Button(button_frame, text="删除", command=on_delete, bg="lightcoral").pack(side=tk.LEFT, padx=5)
        tk.Button(button_frame, text="取消", command=dialog.destroy).pack(side=tk.RIGHT, padx=5)

        # 双击加载
        tree.bind('<Double-1>', lambda e: on_load())

        # 绑定搜索
        search_entry.bind('<KeyRelease>', lambda e: on_search())

    def load_project_data(self, station_name):
        """加载项目数据"""
        project_data = self.db_manager.load_project(station_name)
        if project_data:
            # 清空当前数据
            self.clear_all_entries()

            # 加载客流数据
            periods = ['initial', 'near', 'far']
            for period in periods:
                for flow_type in ['up_in', 'up_out', 'up_area', 'down_in', 'down_out', 'down_area']:
                    key = f'{period}_{flow_type}'
                    if key in project_data:
                        self.passenger_entries[period][flow_type].insert(0, str(project_data[key]))

            # 加载计算参数
            for param_key, entry_or_dict in self.param_entries.items():
                if isinstance(entry_or_dict, dict):
                    # 每个阶段不同的参数
                    for period in periods:
                        key = f'{param_key}_{period}'
                        if key in project_data:
                            entry_or_dict[period].insert(0, str(project_data[key]))
                else:
                    # 所有阶段相同的参数
                    if param_key in project_data:
                        entry_or_dict.insert(0, str(project_data[param_key]))

            # 加载计算结果
            self.calculator.load_data(project_data)
            self.update_results()

            self.current_station_name = station_name
            self.is_data_modified = False
            self.last_saved_data = self.get_current_data_snapshot()
            self.update_window_title()

            messagebox.showinfo("加载成功", f"项目 '{station_name}' 加载成功！")
        else:
            messagebox.showerror("加载失败", f"无法加载项目 '{station_name}'！")

    def get_current_data_snapshot(self):
        """获取当前数据的快照，用于检测变化"""
        try:
            passenger_data, param_data = self.get_input_values()
            if passenger_data is None or param_data is None:
                return None

            # 合并所有数据
            snapshot = {}
            snapshot.update(passenger_data)
            snapshot.update(param_data)
            return snapshot
        except:
            return None

    def export_excel(self):
        """导出当前项目到Excel"""
        if not self.current_station_name:
            messagebox.showwarning("提示", "请先保存或加载一个项目！")
            return

        # 选择保存路径
        file_path = filedialog.asksaveasfilename(
            title="导出Excel文件",
            defaultextension=".xlsx",
            filetypes=[("Excel文件", "*.xlsx"), ("所有文件", "*.*")],
            initialfile=f"{self.current_station_name}.xlsx"
        )

        if file_path:
            project_data = self.db_manager.load_project(self.current_station_name)
            if project_data:
                if self.excel_exporter.export_single_project(project_data, file_path):
                    messagebox.showinfo("导出成功", f"项目已导出到: {file_path}")
                else:
                    messagebox.showerror("导出失败", "导出Excel文件时出现错误！")
            else:
                messagebox.showerror("导出失败", "无法获取项目数据！")

    def batch_export_excel(self):
        """批量导出项目到Excel"""
        projects = self.db_manager.get_all_projects()
        if not projects:
            messagebox.showwarning("提示", "没有可导出的项目！")
            return

        # 选择保存路径
        file_path = filedialog.asksaveasfilename(
            title="批量导出Excel文件",
            defaultextension=".xlsx",
            filetypes=[("Excel文件", "*.xlsx"), ("所有文件", "*.*")],
            initialfile="批量导出.xlsx"
        )

        if file_path:
            # 获取所有项目数据
            projects_data = []
            for station_name, _ in projects:
                project_data = self.db_manager.load_project(station_name)
                if project_data:
                    projects_data.append(project_data)

            if projects_data:
                if self.excel_exporter.export_multiple_projects(projects_data, file_path):
                    messagebox.showinfo("导出成功", f"已导出 {len(projects_data)} 个项目到: {file_path}")
                else:
                    messagebox.showerror("导出失败", "批量导出Excel文件时出现错误！")
            else:
                messagebox.showerror("导出失败", "无法获取项目数据！")

    def show_help(self):
        """显示帮助文档"""
        # 尝试打开外部帮助文档
        help_files = ["帮助文档.rtf", "帮助文档.docx", "帮助文档.doc"]
        help_file_found = None

        for help_file in help_files:
            if os.path.exists(help_file):
                help_file_found = help_file
                break

        if help_file_found:
            try:
                # 使用系统默认程序打开文档
                if os.name == 'nt':  # Windows
                    os.startfile(help_file_found)
                elif os.name == 'posix':  # macOS and Linux
                    os.system(f'open "{help_file_found}"')
                return
            except Exception as e:
                print(f"无法打开帮助文档: {e}")

        # 如果无法打开外部文档，则显示内置帮助
        self.show_builtin_help()

    def show_builtin_help(self):
        """显示内置帮助文档"""
        help_window = tk.Toplevel(self.root)
        help_window.title("地铁站台疏散时间计算器 V1.0 - 帮助文档")
        help_window.geometry("900x700")
        help_window.transient(self.root)
        help_window.grab_set()

        # 居中显示
        help_window.geometry("+%d+%d" % (self.root.winfo_rootx() + 150, self.root.winfo_rooty() + 50))

        # 创建滚动文本框
        main_frame = ttk.Frame(help_window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 创建滚动条
        scrollbar = ttk.Scrollbar(main_frame)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 创建文本框
        text_widget = tk.Text(main_frame, wrap=tk.WORD, yscrollcommand=scrollbar.set,
                             font=('微软雅黑', 10), bg='white', fg='black')
        text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.config(command=text_widget.yview)

        # 帮助文档内容
        help_content = self.get_help_content()
        text_widget.insert(tk.END, help_content)
        text_widget.config(state=tk.DISABLED)  # 设为只读

        # 关闭按钮
        close_button = tk.Button(help_window, text="关闭", command=help_window.destroy,
                               font=('微软雅黑', 10), width=10)
        close_button.pack(pady=10)

def main():
    """主函数"""
    root = tk.Tk()
    app = MetroEvacuationGUIV2(root)
    root.mainloop()


if __name__ == "__main__":
    main()
