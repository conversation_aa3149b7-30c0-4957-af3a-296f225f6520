#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试tkinter内置图片加载功能
"""

import tkinter as tk
import os

def test_tkinter_image():
    """测试tkinter内置的PhotoImage功能"""
    
    # 创建测试窗口
    root = tk.Tk()
    root.title("tkinter图片加载测试")
    root.geometry("500x400")
    
    # 标题
    title_label = tk.Label(root, text="tkinter内置图片加载测试", 
                          font=("微软雅黑", 14, "bold"))
    title_label.pack(pady=10)
    
    # 检查图片文件
    image_path = "软件信息.png"
    
    if not os.path.exists(image_path):
        error_label = tk.Label(root, text=f"图片文件不存在: {image_path}", 
                              font=("微软雅黑", 12), fg="red")
        error_label.pack(pady=20)
        tk.Button(root, text="关闭", command=root.destroy).pack(pady=10)
        root.mainloop()
        return
    
    # 显示文件信息
    file_size = os.path.getsize(image_path)
    info_label = tk.Label(root, text=f"图片文件: {image_path}\n文件大小: {file_size} 字节", 
                         font=("微软雅黑", 10))
    info_label.pack(pady=10)
    
    try:
        # 使用tkinter内置的PhotoImage加载图片
        print(f"正在加载图片: {image_path}")
        image = tk.PhotoImage(file=image_path)
        
        # 获取图片尺寸
        width = image.width()
        height = image.height()
        
        # 显示原始尺寸信息
        size_label = tk.Label(root, text=f"原始尺寸: {width} x {height}", 
                             font=("微软雅黑", 10))
        size_label.pack(pady=5)
        
        # 创建显示区域
        display_frame = tk.Frame(root)
        display_frame.pack(pady=10)
        
        # 原始尺寸显示
        original_label = tk.Label(display_frame, text="原始尺寸:", font=("微软雅黑", 10))
        original_label.pack()
        
        image_label1 = tk.Label(display_frame, image=image)
        image_label1.pack(pady=5)
        
        # 如果图片较大，显示缩放版本
        if width > 300 or height > 200:
            # 计算缩放因子
            scale_x = max(1, width // 300)
            scale_y = max(1, height // 200)
            scale_factor = max(scale_x, scale_y)
            
            # 缩放图片
            scaled_image = image.subsample(scale_factor, scale_factor)
            
            scaled_width = width // scale_factor
            scaled_height = height // scale_factor
            
            # 显示缩放信息
            scale_info = tk.Label(display_frame, 
                                 text=f"缩放版本 (缩放因子: {scale_factor}): {scaled_width} x {scaled_height}", 
                                 font=("微软雅黑", 10))
            scale_info.pack(pady=(10, 0))
            
            # 显示缩放后的图片
            image_label2 = tk.Label(display_frame, image=scaled_image)
            image_label2.pack(pady=5)
            
            # 保存图片引用
            image_label2.image = scaled_image
        
        # 保存图片引用，防止被垃圾回收
        image_label1.image = image
        
        # 成功信息
        success_label = tk.Label(root, text="✓ 图片加载成功！", 
                                font=("微软雅黑", 12), fg="green")
        success_label.pack(pady=10)
        
        print(f"图片加载成功: {width}x{height}")
        
    except tk.TclError as e:
        error_text = f"tkinter加载失败: {str(e)}\n\n可能原因:\n1. 图片格式不支持（tkinter只支持PNG和GIF）\n2. 图片文件损坏"
        error_label = tk.Label(root, text=error_text, 
                              font=("微软雅黑", 10), fg="red", justify=tk.LEFT)
        error_label.pack(pady=20)
        print(f"tkinter加载图片失败: {e}")
        
    except Exception as e:
        error_label = tk.Label(root, text=f"其他错误: {str(e)}", 
                              font=("微软雅黑", 10), fg="red")
        error_label.pack(pady=20)
        print(f"加载图片时出现错误: {e}")
    
    # 关闭按钮
    tk.Button(root, text="关闭", command=root.destroy, 
             font=("微软雅黑", 10)).pack(pady=20)
    
    root.mainloop()

if __name__ == "__main__":
    test_tkinter_image()
