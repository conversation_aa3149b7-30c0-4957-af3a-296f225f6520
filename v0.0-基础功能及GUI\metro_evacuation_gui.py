#!/usr/bin/env python
# -*- coding: utf-8 -*-

import tkinter as tk
from tkinter import ttk, messagebox
import tkinter.font as tkFont
from metro_evacuation_calculator import MetroEvacuationCalculator

class MetroEvacuationGUI:
    """地铁站台疏散时间计算GUI界面"""
    
    def __init__(self, root):
        self.root = root
        self.root.title("地铁站台疏散时间计算器")
        self.root.geometry("800x700")
        
        # 创建计算器实例
        self.calculator = MetroEvacuationCalculator()
        
        # 设置字体
        self.font_title = tkFont.Font(family="微软雅黑", size=14, weight="bold")
        self.font_label = tkFont.Font(family="微软雅黑", size=10)
        self.font_entry = tkFont.Font(family="微软雅黑", size=10)
        
        self.create_widgets()
        self.load_default_values()
    
    def create_widgets(self):
        """创建界面组件"""
        # 主标题
        title_label = tk.Label(self.root, text="地铁站台疏散时间计算器", 
                              font=self.font_title, fg="blue")
        title_label.pack(pady=10)
        
        # 创建主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # 创建左右两列
        left_frame = ttk.Frame(main_frame)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))
        
        right_frame = ttk.Frame(main_frame)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(5, 0))
        
        # 左侧：输入参数
        self.create_input_section(left_frame)
        
        # 右侧：计算结果
        self.create_result_section(right_frame)
        
        # 底部：操作按钮
        self.create_button_section(main_frame)
    
    def create_input_section(self, parent):
        """创建输入参数区域"""
        # 客流数据组
        passenger_group = ttk.LabelFrame(parent, text="客流数据", padding=10)
        passenger_group.pack(fill=tk.X, pady=(0, 10))
        
        self.passenger_entries = {}
        passenger_params = [
            ("up_in", "上行进客", "人"),
            ("up_out", "上行出客", "人"),
            ("up_area", "上行断面", "人"),
            ("down_in", "下行进客", "人"),
            ("down_out", "下行出客", "人"),
            ("down_area", "下行断面", "人"),
        ]
        
        for i, (key, label, unit) in enumerate(passenger_params):
            row = i // 2
            col = (i % 2) * 3
            
            tk.Label(passenger_group, text=f"{label}:", font=self.font_label).grid(
                row=row, column=col, sticky="w", padx=(0, 5), pady=2)
            
            entry = tk.Entry(passenger_group, font=self.font_entry, width=10)
            entry.grid(row=row, column=col+1, padx=(0, 5), pady=2)
            self.passenger_entries[key] = entry
            
            tk.Label(passenger_group, text=unit, font=self.font_label).grid(
                row=row, column=col+2, sticky="w", padx=(0, 15), pady=2)
        
        # 计算参数组
        param_group = ttk.LabelFrame(parent, text="计算参数", padding=10)
        param_group.pack(fill=tk.X, pady=(0, 10))
        
        self.param_entries = {}
        calc_params = [
            ("k", "高峰系数", ""),
            ("n_train", "列车对数", "对"),
            ("p", "站台人流密度", "m²/人"),
            ("l", "站台长度", "m"),
            ("m", "站台边缘安全门内侧距离", "m"),
            ("a_1", "自动扶梯通过能力", "人/h·m"),
            ("a_2", "疏散楼梯通过能力", "人/h·m"),
            ("n_auto", "自动扶梯台数", "台"),
            ("b", "疏散楼梯宽度", "m"),
            ("n_stair", "疏散楼梯数", "个"),
        ]
        
        for i, (key, label, unit) in enumerate(calc_params):
            row = i // 2
            col = (i % 2) * 3
            
            tk.Label(param_group, text=f"{label}:", font=self.font_label).grid(
                row=row, column=col, sticky="w", padx=(0, 5), pady=2)
            
            entry = tk.Entry(param_group, font=self.font_entry, width=10)
            entry.grid(row=row, column=col+1, padx=(0, 5), pady=2)
            self.param_entries[key] = entry
            
            tk.Label(param_group, text=unit, font=self.font_label).grid(
                row=row, column=col+2, sticky="w", padx=(0, 15), pady=2)
    
    def create_result_section(self, parent):
        """创建计算结果区域"""
        result_group = ttk.LabelFrame(parent, text="计算结果", padding=10)
        result_group.pack(fill=tk.BOTH, expand=True)
        
        # 中间计算值
        intermediate_frame = ttk.LabelFrame(result_group, text="中间计算值", padding=5)
        intermediate_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.intermediate_labels = {}
        intermediate_results = [
            ("up_add", "上行单侧上下客合计", "人"),
            ("down_add", "下行单侧上下客合计", "人"),
            ("in_add", "上客流量合计", "人"),
        ]
        
        for i, (key, label, unit) in enumerate(intermediate_results):
            tk.Label(intermediate_frame, text=f"{label}:", font=self.font_label).grid(
                row=i, column=0, sticky="w", padx=(0, 5), pady=2)
            
            result_label = tk.Label(intermediate_frame, text="0.00", font=self.font_entry, 
                                  bg="white", relief="sunken", width=15)
            result_label.grid(row=i, column=1, padx=(0, 5), pady=2)
            self.intermediate_labels[key] = result_label
            
            tk.Label(intermediate_frame, text=unit, font=self.font_label).grid(
                row=i, column=2, sticky="w", pady=2)
        
        # 最终结果
        final_frame = ttk.LabelFrame(result_group, text="最终结果", padding=5)
        final_frame.pack(fill=tk.X)
        
        self.result_labels = {}
        final_results = [
            ("b_1", "站台宽度b1", "m"),
            ("b_2", "站台宽度b2", "m"),
            ("q_1", "Q1", "人"),
            ("q_2", "Q2", "人"),
            ("t", "疏散时间T", "min"),
        ]
        
        for i, (key, label, unit) in enumerate(final_results):
            tk.Label(final_frame, text=f"{label}:", font=self.font_label).grid(
                row=i, column=0, sticky="w", padx=(0, 5), pady=2)
            
            result_label = tk.Label(final_frame, text="0.000000", font=self.font_entry, 
                                  bg="lightyellow", relief="sunken", width=15)
            result_label.grid(row=i, column=1, padx=(0, 5), pady=2)
            self.result_labels[key] = result_label
            
            tk.Label(final_frame, text=unit, font=self.font_label).grid(
                row=i, column=2, sticky="w", pady=2)
    
    def create_button_section(self, parent):
        """创建按钮区域"""
        button_frame = ttk.Frame(parent)
        button_frame.pack(fill=tk.X, pady=10)
        
        # 计算按钮
        calc_button = tk.Button(button_frame, text="计算", font=self.font_title,
                               bg="lightgreen", command=self.calculate)
        calc_button.pack(side=tk.LEFT, padx=(0, 10))
        
        # 重置按钮
        reset_button = tk.Button(button_frame, text="重置", font=self.font_label,
                                command=self.reset_values)
        reset_button.pack(side=tk.LEFT, padx=(0, 10))
        
        # 加载示例按钮
        example_button = tk.Button(button_frame, text="加载示例", font=self.font_label,
                                  command=self.load_example)
        example_button.pack(side=tk.LEFT)
    
    def load_default_values(self):
        """加载默认值"""
        # 设置默认的计算参数
        defaults = {
            'k': '1.13',
            'n_train': '20',
            'p': '0.5',
            'l': '135.6',
            'm': '0.3',
            'a_1': '7300',
            'a_2': '3700',
            'n_auto': '2',
            'b': '3.3',
            'n_stair': '1'
        }
        
        for key, value in defaults.items():
            if key in self.param_entries:
                self.param_entries[key].insert(0, value)
    
    def load_example(self):
        """加载示例数据"""
        # 清空现有数据
        self.clear_entries()
        
        # 加载示例客流数据
        example_passenger = {
            'up_in': '2767',
            'up_out': '331',
            'up_area': '7785',
            'down_in': '654',
            'down_out': '739',
            'down_area': '6393'
        }
        
        for key, value in example_passenger.items():
            self.passenger_entries[key].insert(0, value)
        
        # 重新加载默认参数
        self.load_default_values()
    
    def clear_entries(self):
        """清空所有输入框"""
        for entry in self.passenger_entries.values():
            entry.delete(0, tk.END)
        for entry in self.param_entries.values():
            entry.delete(0, tk.END)
    
    def reset_values(self):
        """重置所有值"""
        self.clear_entries()
        self.load_default_values()
        
        # 清空结果显示
        for label in self.intermediate_labels.values():
            label.config(text="0.00")
        for label in self.result_labels.values():
            label.config(text="0.000000")
    
    def get_input_values(self):
        """获取输入值"""
        try:
            # 获取客流数据
            passenger_data = {}
            for key, entry in self.passenger_entries.items():
                value = entry.get().strip()
                if not value:
                    raise ValueError(f"请输入{key}")
                passenger_data[key] = float(value)
            
            # 获取计算参数
            param_data = {}
            for key, entry in self.param_entries.items():
                value = entry.get().strip()
                if not value:
                    raise ValueError(f"请输入{key}")
                param_data[key] = float(value)
            
            return passenger_data, param_data
            
        except ValueError as e:
            messagebox.showerror("输入错误", str(e))
            return None, None
    
    def calculate(self):
        """执行计算"""
        passenger_data, param_data = self.get_input_values()
        if passenger_data is None or param_data is None:
            return
        
        try:
            # 设置计算器参数
            self.calculator.set_passenger_flow(
                passenger_data['up_in'], passenger_data['up_out'], passenger_data['up_area'],
                passenger_data['down_in'], passenger_data['down_out'], passenger_data['down_area']
            )
            
            self.calculator.set_parameters(
                k=param_data['k'], n_train=param_data['n_train'], p=param_data['p'],
                l=param_data['l'], m=param_data['m'], a_1=param_data['a_1'],
                a_2=param_data['a_2'], n_auto=param_data['n_auto'], 
                b=param_data['b'], n_stair=param_data['n_stair']
            )
            
            # 执行计算
            self.calculator.calculate_all()
            
            # 更新结果显示
            self.update_results()
            
        except Exception as e:
            messagebox.showerror("计算错误", f"计算过程中出现错误：{str(e)}")
    
    def update_results(self):
        """更新结果显示"""
        # 更新中间计算值
        self.intermediate_labels['up_add'].config(text=f"{self.calculator.up_add:.2f}")
        self.intermediate_labels['down_add'].config(text=f"{self.calculator.down_add:.2f}")
        self.intermediate_labels['in_add'].config(text=f"{self.calculator.in_add:.2f}")
        
        # 更新最终结果
        self.result_labels['b_1'].config(text=f"{self.calculator.b_1:.6f}")
        self.result_labels['b_2'].config(text=f"{self.calculator.b_2:.6f}")
        self.result_labels['q_1'].config(text=f"{self.calculator.q_1:.4f}")
        self.result_labels['q_2'].config(text=f"{self.calculator.q_2:.4f}")
        self.result_labels['t'].config(text=f"{self.calculator.t:.6f}")


def main():
    """主函数"""
    root = tk.Tk()
    app = MetroEvacuationGUI(root)
    root.mainloop()


if __name__ == "__main__":
    main()
