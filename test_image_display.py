#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试图片显示功能
"""

import tkinter as tk
import os

try:
    from PIL import Image, ImageTk
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False

def test_image_display():
    """测试图片显示功能"""
    
    # 创建测试窗口
    root = tk.Tk()
    root.title("图片显示测试")
    root.geometry("400x300")
    
    # 检查图片文件
    image_path = "软件信息.png"
    
    if not os.path.exists(image_path):
        tk.Label(root, text=f"图片文件不存在: {image_path}", 
                font=("微软雅黑", 12), fg="red").pack(pady=50)
        tk.Button(root, text="关闭", command=root.destroy).pack(pady=10)
        root.mainloop()
        return
    
    # 显示图片信息
    info_label = tk.Label(root, text=f"图片文件: {image_path}", 
                         font=("微软雅黑", 10))
    info_label.pack(pady=10)
    
    # 显示PIL可用性
    pil_status = "PIL库可用" if PIL_AVAILABLE else "PIL库不可用"
    pil_label = tk.Label(root, text=pil_status, 
                        font=("微软雅黑", 10),
                        fg="green" if PIL_AVAILABLE else "red")
    pil_label.pack(pady=5)
    
    if PIL_AVAILABLE:
        try:
            # 加载图片
            image = Image.open(image_path)
            original_size = image.size
            
            # 显示原始尺寸
            size_label = tk.Label(root, text=f"原始尺寸: {original_size[0]} x {original_size[1]}", 
                                 font=("微软雅黑", 10))
            size_label.pack(pady=5)
            
            # 调整图片大小
            max_width = 200
            max_height = 100
            
            width_ratio = max_width / original_size[0]
            height_ratio = max_height / original_size[1]
            scale_ratio = min(width_ratio, height_ratio)
            
            new_width = int(original_size[0] * scale_ratio)
            new_height = int(original_size[1] * scale_ratio)
            
            # 缩放图片
            image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)
            
            # 转换为tkinter格式
            photo = ImageTk.PhotoImage(image)
            
            # 显示调整后尺寸
            new_size_label = tk.Label(root, text=f"显示尺寸: {new_width} x {new_height}", 
                                     font=("微软雅黑", 10))
            new_size_label.pack(pady=5)
            
            # 显示图片
            image_label = tk.Label(root, image=photo, relief="sunken", bd=2)
            image_label.pack(pady=10)
            
            # 保持图片引用
            image_label.image = photo
            
            status_label = tk.Label(root, text="✓ 图片加载成功", 
                                   font=("微软雅黑", 10), fg="green")
            status_label.pack(pady=5)
            
        except Exception as e:
            error_label = tk.Label(root, text=f"图片加载失败: {str(e)}", 
                                  font=("微软雅黑", 10), fg="red")
            error_label.pack(pady=10)
    else:
        # 备用显示
        fallback_label = tk.Label(root, text="软件信息图片区域\n(需要PIL库支持)", 
                                 font=("微软雅黑", 10), 
                                 bg="lightgray", relief="sunken",
                                 width=30, height=5)
        fallback_label.pack(pady=20)
    
    # 关闭按钮
    tk.Button(root, text="关闭", command=root.destroy, 
             font=("微软雅黑", 10)).pack(pady=10)
    
    root.mainloop()

if __name__ == "__main__":
    test_image_display()
