#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
地铁站台疏散时间计算器 V10
支持三个阶段（初期、近期、远期）的计算
"""

class MetroEvacuationCalculatorV2:
    """地铁站台疏散时间计算器 V1.0"""
    
    def __init__(self):
        """初始化计算器"""
        # 客流数据 - 三个阶段
        self.periods = ['initial', 'near', 'far']
        
        # 初始化所有阶段的客流数据
        for period in self.periods:
            setattr(self, f'{period}_up_in', 0)      # 上行进客
            setattr(self, f'{period}_up_out', 0)     # 上行出客
            setattr(self, f'{period}_up_area', 0)    # 上行断面
            setattr(self, f'{period}_down_in', 0)    # 下行进客
            setattr(self, f'{period}_down_out', 0)   # 下行出客
            setattr(self, f'{period}_down_area', 0)  # 下行断面
            
            # 中间计算值
            setattr(self, f'{period}_up_add', 0)     # 上行单侧上下客合计
            setattr(self, f'{period}_down_add', 0)   # 下行单侧上下客合计
            setattr(self, f'{period}_in_add', 0)     # 上客流量合计
            
            # 最终结果
            setattr(self, f'{period}_b_1', 0)        # 站台宽度b1
            setattr(self, f'{period}_b_2', 0)        # 站台宽度b2
            setattr(self, f'{period}_q_1', 0)        # Q1
            setattr(self, f'{period}_q_2', 0)        # Q2
            setattr(self, f'{period}_t', 0)          # 疏散时间T
        
        # 计算参数 - 每个阶段都有独立的参数
        # 初期参数
        self.k_initial = 1.13       # 初期高峰系数
        self.n_train_initial = 20   # 初期列车对数
        self.p_initial = 0.5        # 初期站台人流密度(人/m²)
        self.l_initial = 135.6      # 初期站台长度(m)
        self.m_initial = 0.3        # 初期站台边缘安全门内侧距离(m)
        self.a_1_initial = 7300     # 初期自动扶梯通过能力(人/h·m)
        self.a_2_initial = 3700     # 初期疏散楼梯通过能力(人/h·m)
        self.n_auto_initial = 2     # 初期自动扶梯台数
        self.b_initial = 3.3        # 初期疏散楼梯宽度(m)
        self.n_stair_initial = 1    # 初期疏散楼梯数

        # 近期参数
        self.k_near = 1.13          # 近期高峰系数
        self.n_train_near = 20      # 近期列车对数
        self.p_near = 0.5           # 近期站台人流密度(人/m²)
        self.l_near = 135.6         # 近期站台长度(m)
        self.m_near = 0.3           # 近期站台边缘安全门内侧距离(m)
        self.a_1_near = 7300        # 近期自动扶梯通过能力(人/h·m)
        self.a_2_near = 3700        # 近期疏散楼梯通过能力(人/h·m)
        self.n_auto_near = 2        # 近期自动扶梯台数
        self.b_near = 3.3           # 近期疏散楼梯宽度(m)
        self.n_stair_near = 1       # 近期疏散楼梯数

        # 远期参数
        self.k_far = 1.13           # 远期高峰系数
        self.n_train_far = 20       # 远期列车对数
        self.p_far = 0.5            # 远期站台人流密度(人/m²)
        self.l_far = 135.6          # 远期站台长度(m)
        self.m_far = 0.3            # 远期站台边缘安全门内侧距离(m)
        self.a_1_far = 7300         # 远期自动扶梯通过能力(人/h·m)
        self.a_2_far = 3700         # 远期疏散楼梯通过能力(人/h·m)
        self.n_auto_far = 2         # 远期自动扶梯台数
        self.b_far = 3.3            # 远期疏散楼梯宽度(m)
        self.n_stair_far = 1        # 远期疏散楼梯数
    
    def set_passenger_flow(self, period: str, up_in: float, up_out: float, up_area: float, 
                          down_in: float, down_out: float, down_area: float):
        """设置指定阶段的客流数据"""
        if period not in self.periods:
            raise ValueError(f"无效的阶段: {period}")
        
        setattr(self, f'{period}_up_in', up_in)
        setattr(self, f'{period}_up_out', up_out)
        setattr(self, f'{period}_up_area', up_area)
        setattr(self, f'{period}_down_in', down_in)
        setattr(self, f'{period}_down_out', down_out)
        setattr(self, f'{period}_down_area', down_area)
    
    def set_parameters(self, **kwargs):
        """设置计算参数 - 支持所有阶段的所有参数"""
        # 初期参数
        if 'k_initial' in kwargs: self.k_initial = kwargs['k_initial']
        if 'n_train_initial' in kwargs: self.n_train_initial = kwargs['n_train_initial']
        if 'p_initial' in kwargs: self.p_initial = kwargs['p_initial']
        if 'l_initial' in kwargs: self.l_initial = kwargs['l_initial']
        if 'm_initial' in kwargs: self.m_initial = kwargs['m_initial']
        if 'a_1_initial' in kwargs: self.a_1_initial = kwargs['a_1_initial']
        if 'a_2_initial' in kwargs: self.a_2_initial = kwargs['a_2_initial']
        if 'n_auto_initial' in kwargs: self.n_auto_initial = kwargs['n_auto_initial']
        if 'b_initial' in kwargs: self.b_initial = kwargs['b_initial']
        if 'n_stair_initial' in kwargs: self.n_stair_initial = kwargs['n_stair_initial']

        # 近期参数
        if 'k_near' in kwargs: self.k_near = kwargs['k_near']
        if 'n_train_near' in kwargs: self.n_train_near = kwargs['n_train_near']
        if 'p_near' in kwargs: self.p_near = kwargs['p_near']
        if 'l_near' in kwargs: self.l_near = kwargs['l_near']
        if 'm_near' in kwargs: self.m_near = kwargs['m_near']
        if 'a_1_near' in kwargs: self.a_1_near = kwargs['a_1_near']
        if 'a_2_near' in kwargs: self.a_2_near = kwargs['a_2_near']
        if 'n_auto_near' in kwargs: self.n_auto_near = kwargs['n_auto_near']
        if 'b_near' in kwargs: self.b_near = kwargs['b_near']
        if 'n_stair_near' in kwargs: self.n_stair_near = kwargs['n_stair_near']

        # 远期参数
        if 'k_far' in kwargs: self.k_far = kwargs['k_far']
        if 'n_train_far' in kwargs: self.n_train_far = kwargs['n_train_far']
        if 'p_far' in kwargs: self.p_far = kwargs['p_far']
        if 'l_far' in kwargs: self.l_far = kwargs['l_far']
        if 'm_far' in kwargs: self.m_far = kwargs['m_far']
        if 'a_1_far' in kwargs: self.a_1_far = kwargs['a_1_far']
        if 'a_2_far' in kwargs: self.a_2_far = kwargs['a_2_far']
        if 'n_auto_far' in kwargs: self.n_auto_far = kwargs['n_auto_far']
        if 'b_far' in kwargs: self.b_far = kwargs['b_far']
        if 'n_stair_far' in kwargs: self.n_stair_far = kwargs['n_stair_far']
    
    def calculate_intermediate_values(self, period: str):
        """计算指定阶段的中间值"""
        if period not in self.periods:
            raise ValueError(f"无效的阶段: {period}")
        
        up_in = getattr(self, f'{period}_up_in')
        up_out = getattr(self, f'{period}_up_out')
        down_in = getattr(self, f'{period}_down_in')
        down_out = getattr(self, f'{period}_down_out')
        
        # 计算单侧上下客合计
        setattr(self, f'{period}_up_add', up_in + up_out)
        setattr(self, f'{period}_down_add', down_in + down_out)
        setattr(self, f'{period}_in_add', up_in + down_in)
    
    def calculate_platform_width(self, period: str):
        """计算指定阶段的站台宽度"""
        if period not in self.periods:
            raise ValueError(f"无效的阶段: {period}")

        up_add = getattr(self, f'{period}_up_add')
        down_add = getattr(self, f'{period}_down_add')
        k = getattr(self, f'k_{period}')
        n_train = getattr(self, f'n_train_{period}')
        p = getattr(self, f'p_{period}')
        l = getattr(self, f'l_{period}')
        m = getattr(self, f'm_{period}')

        if l > 0 and n_train > 0:
            # b1计算：上行进出站客流合计相关
            # 公式：=C8*C13/C14*C15/C16+C17
            b_1 = (up_add * k / n_train * p / l) + m
            setattr(self, f'{period}_b_1', b_1)

            # b2计算：下行进出站客流合计相关
            # 公式：=C9*C13/C14*C15/C16+C17
            b_2 = (down_add * k / n_train * p / l) + m
            setattr(self, f'{period}_b_2', b_2)
        else:
            setattr(self, f'{period}_b_1', 0)
            setattr(self, f'{period}_b_2', 0)
    
    def calculate_evacuation_flow(self, period: str):
        """计算指定阶段的疏散人流"""
        if period not in self.periods:
            raise ValueError(f"无效的阶段: {period}")

        up_area = getattr(self, f'{period}_up_area')
        down_area = getattr(self, f'{period}_down_area')
        in_add = getattr(self, f'{period}_in_add')
        k = getattr(self, f'k_{period}')
        n_train = getattr(self, f'n_train_{period}')

        if n_train > 0:
            # Q1：根据上行和下行断面客流的大小关系计算
            # 公式：=IF(C4<C7,C7/C14*C13,C4/C14*C13)
            if up_area < down_area:
                q_1 = (down_area / n_train) * k
            else:
                q_1 = (up_area / n_train) * k
            setattr(self, f'{period}_q_1', q_1)

            # Q2：进站客流合计相关
            # 公式：=C10/C14*C13
            q_2 = (in_add / n_train) * k
            setattr(self, f'{period}_q_2', q_2)
        else:
            setattr(self, f'{period}_q_1', 0)
            setattr(self, f'{period}_q_2', 0)
    
    def calculate_evacuation_time(self, period: str):
        """计算指定阶段的疏散时间"""
        if period not in self.periods:
            raise ValueError(f"无效的阶段: {period}")

        q_1 = getattr(self, f'{period}_q_1')
        q_2 = getattr(self, f'{period}_q_2')
        a_1 = getattr(self, f'a_1_{period}')
        a_2 = getattr(self, f'a_2_{period}')
        n_auto = getattr(self, f'n_auto_{period}')
        n_stair = getattr(self, f'n_stair_{period}')
        b = getattr(self, f'b_{period}')

        # 基于Excel表格中的精确公式
        # T = (C27+C28)/0.9/(C18/60*(C20-1)+C19/60*C22*C21)
        # T = (q_1+q_2)/0.9/(a_1/60*(n_auto-1)+a_2/60*n_stair*b)

        # 计算分母：疏散能力
        # 自动扶梯能力：a_1/60*(n_auto-1)
        escalator_capacity_per_min = (a_1 / 60) * (n_auto - 1)

        # 疏散楼梯能力：a_2/60*n_stair*b
        stair_capacity_per_min = (a_2 / 60) * n_stair * b

        # 总疏散能力（人/分钟）
        total_capacity_per_min = escalator_capacity_per_min + stair_capacity_per_min

        if total_capacity_per_min > 0:
            # 疏散时间计算
            # 公式：=(C27+C28)/0.9/(C18/60*(C20-1)+C19/60*C22*C21)
            t = (q_1 + q_2) / 0.9 / total_capacity_per_min
            setattr(self, f'{period}_t', t)
        else:
            setattr(self, f'{period}_t', 0)
    
    def calculate_period(self, period: str):
        """计算指定阶段的所有值"""
        self.calculate_intermediate_values(period)
        self.calculate_platform_width(period)
        self.calculate_evacuation_flow(period)
        self.calculate_evacuation_time(period)
    
    def calculate_all_periods(self):
        """计算所有阶段"""
        for period in self.periods:
            self.calculate_period(period)
    
    def get_period_results(self, period: str) -> dict:
        """获取指定阶段的计算结果"""
        if period not in self.periods:
            raise ValueError(f"无效的阶段: {period}")
        
        return {
            'up_add': getattr(self, f'{period}_up_add'),
            'down_add': getattr(self, f'{period}_down_add'),
            'in_add': getattr(self, f'{period}_in_add'),
            'b_1': getattr(self, f'{period}_b_1'),
            'b_2': getattr(self, f'{period}_b_2'),
            'q_1': getattr(self, f'{period}_q_1'),
            'q_2': getattr(self, f'{period}_q_2'),
            't': getattr(self, f'{period}_t')
        }
    
    def get_all_results(self) -> dict:
        """获取所有阶段的计算结果"""
        results = {}
        for period in self.periods:
            results[period] = self.get_period_results(period)
        return results
    
    def get_all_data(self) -> dict:
        """获取所有数据（输入参数+计算结果）"""
        data = {}
        
        # 客流数据
        for period in self.periods:
            data[f'{period}_up_in'] = getattr(self, f'{period}_up_in')
            data[f'{period}_up_out'] = getattr(self, f'{period}_up_out')
            data[f'{period}_up_area'] = getattr(self, f'{period}_up_area')
            data[f'{period}_down_in'] = getattr(self, f'{period}_down_in')
            data[f'{period}_down_out'] = getattr(self, f'{period}_down_out')
            data[f'{period}_down_area'] = getattr(self, f'{period}_down_area')
            
            # 中间计算值
            data[f'{period}_up_add'] = getattr(self, f'{period}_up_add')
            data[f'{period}_down_add'] = getattr(self, f'{period}_down_add')
            data[f'{period}_in_add'] = getattr(self, f'{period}_in_add')
            
            # 最终结果
            data[f'{period}_b_1'] = getattr(self, f'{period}_b_1')
            data[f'{period}_b_2'] = getattr(self, f'{period}_b_2')
            data[f'{period}_q_1'] = getattr(self, f'{period}_q_1')
            data[f'{period}_q_2'] = getattr(self, f'{period}_q_2')
            data[f'{period}_t'] = getattr(self, f'{period}_t')
        
        # 计算参数 - 所有阶段的所有参数
        # 初期参数
        data['k_initial'] = self.k_initial
        data['n_train_initial'] = self.n_train_initial
        data['p_initial'] = self.p_initial
        data['l_initial'] = self.l_initial
        data['m_initial'] = self.m_initial
        data['a_1_initial'] = self.a_1_initial
        data['a_2_initial'] = self.a_2_initial
        data['n_auto_initial'] = self.n_auto_initial
        data['b_initial'] = self.b_initial
        data['n_stair_initial'] = self.n_stair_initial

        # 近期参数
        data['k_near'] = self.k_near
        data['n_train_near'] = self.n_train_near
        data['p_near'] = self.p_near
        data['l_near'] = self.l_near
        data['m_near'] = self.m_near
        data['a_1_near'] = self.a_1_near
        data['a_2_near'] = self.a_2_near
        data['n_auto_near'] = self.n_auto_near
        data['b_near'] = self.b_near
        data['n_stair_near'] = self.n_stair_near

        # 远期参数
        data['k_far'] = self.k_far
        data['n_train_far'] = self.n_train_far
        data['p_far'] = self.p_far
        data['l_far'] = self.l_far
        data['m_far'] = self.m_far
        data['a_1_far'] = self.a_1_far
        data['a_2_far'] = self.a_2_far
        data['n_auto_far'] = self.n_auto_far
        data['b_far'] = self.b_far
        data['n_stair_far'] = self.n_stair_far
        
        return data
    
    def load_data(self, data: dict):
        """加载数据到计算器"""
        # 加载客流数据和结果
        for period in self.periods:
            for attr in ['up_in', 'up_out', 'up_area', 'down_in', 'down_out', 'down_area',
                        'up_add', 'down_add', 'in_add', 'b_1', 'b_2', 'q_1', 'q_2', 't']:
                key = f'{period}_{attr}'
                if key in data:
                    setattr(self, key, data[key])
        
        # 加载计算参数 - 所有阶段的所有参数
        param_keys = [
            # 初期参数
            'k_initial', 'n_train_initial', 'p_initial', 'l_initial', 'm_initial',
            'a_1_initial', 'a_2_initial', 'n_auto_initial', 'b_initial', 'n_stair_initial',
            # 近期参数
            'k_near', 'n_train_near', 'p_near', 'l_near', 'm_near',
            'a_1_near', 'a_2_near', 'n_auto_near', 'b_near', 'n_stair_near',
            # 远期参数
            'k_far', 'n_train_far', 'p_far', 'l_far', 'm_far',
            'a_1_far', 'a_2_far', 'n_auto_far', 'b_far', 'n_stair_far'
        ]
        for key in param_keys:
            if key in data:
                setattr(self, key, data[key])
